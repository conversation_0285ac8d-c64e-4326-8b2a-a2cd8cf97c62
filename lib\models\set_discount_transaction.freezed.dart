// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'set_discount_transaction.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SetDiscountTransaction {

 int? get id; String get batchSaleId;// 어떤 거래에 적용되었는지
 List<AppliedSetDiscountData> get appliedDiscounts;// 적용된 세트 할인들
 int get totalDiscountAmount;// 총 할인 금액
 int get appliedCount;// 실제 세트 할인 적용 횟수
 DateTime get createdAt; int get eventId;
/// Create a copy of SetDiscountTransaction
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SetDiscountTransactionCopyWith<SetDiscountTransaction> get copyWith => _$SetDiscountTransactionCopyWithImpl<SetDiscountTransaction>(this as SetDiscountTransaction, _$identity);

  /// Serializes this SetDiscountTransaction to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SetDiscountTransaction&&(identical(other.id, id) || other.id == id)&&(identical(other.batchSaleId, batchSaleId) || other.batchSaleId == batchSaleId)&&const DeepCollectionEquality().equals(other.appliedDiscounts, appliedDiscounts)&&(identical(other.totalDiscountAmount, totalDiscountAmount) || other.totalDiscountAmount == totalDiscountAmount)&&(identical(other.appliedCount, appliedCount) || other.appliedCount == appliedCount)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.eventId, eventId) || other.eventId == eventId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,batchSaleId,const DeepCollectionEquality().hash(appliedDiscounts),totalDiscountAmount,appliedCount,createdAt,eventId);

@override
String toString() {
  return 'SetDiscountTransaction(id: $id, batchSaleId: $batchSaleId, appliedDiscounts: $appliedDiscounts, totalDiscountAmount: $totalDiscountAmount, appliedCount: $appliedCount, createdAt: $createdAt, eventId: $eventId)';
}


}

/// @nodoc
abstract mixin class $SetDiscountTransactionCopyWith<$Res>  {
  factory $SetDiscountTransactionCopyWith(SetDiscountTransaction value, $Res Function(SetDiscountTransaction) _then) = _$SetDiscountTransactionCopyWithImpl;
@useResult
$Res call({
 int? id, String batchSaleId, List<AppliedSetDiscountData> appliedDiscounts, int totalDiscountAmount, int appliedCount, DateTime createdAt, int eventId
});




}
/// @nodoc
class _$SetDiscountTransactionCopyWithImpl<$Res>
    implements $SetDiscountTransactionCopyWith<$Res> {
  _$SetDiscountTransactionCopyWithImpl(this._self, this._then);

  final SetDiscountTransaction _self;
  final $Res Function(SetDiscountTransaction) _then;

/// Create a copy of SetDiscountTransaction
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? batchSaleId = null,Object? appliedDiscounts = null,Object? totalDiscountAmount = null,Object? appliedCount = null,Object? createdAt = null,Object? eventId = null,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,batchSaleId: null == batchSaleId ? _self.batchSaleId : batchSaleId // ignore: cast_nullable_to_non_nullable
as String,appliedDiscounts: null == appliedDiscounts ? _self.appliedDiscounts : appliedDiscounts // ignore: cast_nullable_to_non_nullable
as List<AppliedSetDiscountData>,totalDiscountAmount: null == totalDiscountAmount ? _self.totalDiscountAmount : totalDiscountAmount // ignore: cast_nullable_to_non_nullable
as int,appliedCount: null == appliedCount ? _self.appliedCount : appliedCount // ignore: cast_nullable_to_non_nullable
as int,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,eventId: null == eventId ? _self.eventId : eventId // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [SetDiscountTransaction].
extension SetDiscountTransactionPatterns on SetDiscountTransaction {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SetDiscountTransaction value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SetDiscountTransaction() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SetDiscountTransaction value)  $default,){
final _that = this;
switch (_that) {
case _SetDiscountTransaction():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SetDiscountTransaction value)?  $default,){
final _that = this;
switch (_that) {
case _SetDiscountTransaction() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int? id,  String batchSaleId,  List<AppliedSetDiscountData> appliedDiscounts,  int totalDiscountAmount,  int appliedCount,  DateTime createdAt,  int eventId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SetDiscountTransaction() when $default != null:
return $default(_that.id,_that.batchSaleId,_that.appliedDiscounts,_that.totalDiscountAmount,_that.appliedCount,_that.createdAt,_that.eventId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int? id,  String batchSaleId,  List<AppliedSetDiscountData> appliedDiscounts,  int totalDiscountAmount,  int appliedCount,  DateTime createdAt,  int eventId)  $default,) {final _that = this;
switch (_that) {
case _SetDiscountTransaction():
return $default(_that.id,_that.batchSaleId,_that.appliedDiscounts,_that.totalDiscountAmount,_that.appliedCount,_that.createdAt,_that.eventId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int? id,  String batchSaleId,  List<AppliedSetDiscountData> appliedDiscounts,  int totalDiscountAmount,  int appliedCount,  DateTime createdAt,  int eventId)?  $default,) {final _that = this;
switch (_that) {
case _SetDiscountTransaction() when $default != null:
return $default(_that.id,_that.batchSaleId,_that.appliedDiscounts,_that.totalDiscountAmount,_that.appliedCount,_that.createdAt,_that.eventId);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SetDiscountTransaction implements SetDiscountTransaction {
  const _SetDiscountTransaction({this.id, required this.batchSaleId, required final  List<AppliedSetDiscountData> appliedDiscounts, required this.totalDiscountAmount, required this.appliedCount, required this.createdAt, required this.eventId}): _appliedDiscounts = appliedDiscounts;
  factory _SetDiscountTransaction.fromJson(Map<String, dynamic> json) => _$SetDiscountTransactionFromJson(json);

@override final  int? id;
@override final  String batchSaleId;
// 어떤 거래에 적용되었는지
 final  List<AppliedSetDiscountData> _appliedDiscounts;
// 어떤 거래에 적용되었는지
@override List<AppliedSetDiscountData> get appliedDiscounts {
  if (_appliedDiscounts is EqualUnmodifiableListView) return _appliedDiscounts;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_appliedDiscounts);
}

// 적용된 세트 할인들
@override final  int totalDiscountAmount;
// 총 할인 금액
@override final  int appliedCount;
// 실제 세트 할인 적용 횟수
@override final  DateTime createdAt;
@override final  int eventId;

/// Create a copy of SetDiscountTransaction
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SetDiscountTransactionCopyWith<_SetDiscountTransaction> get copyWith => __$SetDiscountTransactionCopyWithImpl<_SetDiscountTransaction>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SetDiscountTransactionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SetDiscountTransaction&&(identical(other.id, id) || other.id == id)&&(identical(other.batchSaleId, batchSaleId) || other.batchSaleId == batchSaleId)&&const DeepCollectionEquality().equals(other._appliedDiscounts, _appliedDiscounts)&&(identical(other.totalDiscountAmount, totalDiscountAmount) || other.totalDiscountAmount == totalDiscountAmount)&&(identical(other.appliedCount, appliedCount) || other.appliedCount == appliedCount)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.eventId, eventId) || other.eventId == eventId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,batchSaleId,const DeepCollectionEquality().hash(_appliedDiscounts),totalDiscountAmount,appliedCount,createdAt,eventId);

@override
String toString() {
  return 'SetDiscountTransaction(id: $id, batchSaleId: $batchSaleId, appliedDiscounts: $appliedDiscounts, totalDiscountAmount: $totalDiscountAmount, appliedCount: $appliedCount, createdAt: $createdAt, eventId: $eventId)';
}


}

/// @nodoc
abstract mixin class _$SetDiscountTransactionCopyWith<$Res> implements $SetDiscountTransactionCopyWith<$Res> {
  factory _$SetDiscountTransactionCopyWith(_SetDiscountTransaction value, $Res Function(_SetDiscountTransaction) _then) = __$SetDiscountTransactionCopyWithImpl;
@override @useResult
$Res call({
 int? id, String batchSaleId, List<AppliedSetDiscountData> appliedDiscounts, int totalDiscountAmount, int appliedCount, DateTime createdAt, int eventId
});




}
/// @nodoc
class __$SetDiscountTransactionCopyWithImpl<$Res>
    implements _$SetDiscountTransactionCopyWith<$Res> {
  __$SetDiscountTransactionCopyWithImpl(this._self, this._then);

  final _SetDiscountTransaction _self;
  final $Res Function(_SetDiscountTransaction) _then;

/// Create a copy of SetDiscountTransaction
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? batchSaleId = null,Object? appliedDiscounts = null,Object? totalDiscountAmount = null,Object? appliedCount = null,Object? createdAt = null,Object? eventId = null,}) {
  return _then(_SetDiscountTransaction(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,batchSaleId: null == batchSaleId ? _self.batchSaleId : batchSaleId // ignore: cast_nullable_to_non_nullable
as String,appliedDiscounts: null == appliedDiscounts ? _self._appliedDiscounts : appliedDiscounts // ignore: cast_nullable_to_non_nullable
as List<AppliedSetDiscountData>,totalDiscountAmount: null == totalDiscountAmount ? _self.totalDiscountAmount : totalDiscountAmount // ignore: cast_nullable_to_non_nullable
as int,appliedCount: null == appliedCount ? _self.appliedCount : appliedCount // ignore: cast_nullable_to_non_nullable
as int,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,eventId: null == eventId ? _self.eventId : eventId // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}


/// @nodoc
mixin _$AppliedSetDiscountData {

 int get setDiscountId;// 세트 할인 ID 추가
 String get setDiscountName; int get discountAmount; int get appliedCount; SetDiscountConditionType get conditionType;
/// Create a copy of AppliedSetDiscountData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AppliedSetDiscountDataCopyWith<AppliedSetDiscountData> get copyWith => _$AppliedSetDiscountDataCopyWithImpl<AppliedSetDiscountData>(this as AppliedSetDiscountData, _$identity);

  /// Serializes this AppliedSetDiscountData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AppliedSetDiscountData&&(identical(other.setDiscountId, setDiscountId) || other.setDiscountId == setDiscountId)&&(identical(other.setDiscountName, setDiscountName) || other.setDiscountName == setDiscountName)&&(identical(other.discountAmount, discountAmount) || other.discountAmount == discountAmount)&&(identical(other.appliedCount, appliedCount) || other.appliedCount == appliedCount)&&(identical(other.conditionType, conditionType) || other.conditionType == conditionType));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,setDiscountId,setDiscountName,discountAmount,appliedCount,conditionType);

@override
String toString() {
  return 'AppliedSetDiscountData(setDiscountId: $setDiscountId, setDiscountName: $setDiscountName, discountAmount: $discountAmount, appliedCount: $appliedCount, conditionType: $conditionType)';
}


}

/// @nodoc
abstract mixin class $AppliedSetDiscountDataCopyWith<$Res>  {
  factory $AppliedSetDiscountDataCopyWith(AppliedSetDiscountData value, $Res Function(AppliedSetDiscountData) _then) = _$AppliedSetDiscountDataCopyWithImpl;
@useResult
$Res call({
 int setDiscountId, String setDiscountName, int discountAmount, int appliedCount, SetDiscountConditionType conditionType
});




}
/// @nodoc
class _$AppliedSetDiscountDataCopyWithImpl<$Res>
    implements $AppliedSetDiscountDataCopyWith<$Res> {
  _$AppliedSetDiscountDataCopyWithImpl(this._self, this._then);

  final AppliedSetDiscountData _self;
  final $Res Function(AppliedSetDiscountData) _then;

/// Create a copy of AppliedSetDiscountData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? setDiscountId = null,Object? setDiscountName = null,Object? discountAmount = null,Object? appliedCount = null,Object? conditionType = null,}) {
  return _then(_self.copyWith(
setDiscountId: null == setDiscountId ? _self.setDiscountId : setDiscountId // ignore: cast_nullable_to_non_nullable
as int,setDiscountName: null == setDiscountName ? _self.setDiscountName : setDiscountName // ignore: cast_nullable_to_non_nullable
as String,discountAmount: null == discountAmount ? _self.discountAmount : discountAmount // ignore: cast_nullable_to_non_nullable
as int,appliedCount: null == appliedCount ? _self.appliedCount : appliedCount // ignore: cast_nullable_to_non_nullable
as int,conditionType: null == conditionType ? _self.conditionType : conditionType // ignore: cast_nullable_to_non_nullable
as SetDiscountConditionType,
  ));
}

}


/// Adds pattern-matching-related methods to [AppliedSetDiscountData].
extension AppliedSetDiscountDataPatterns on AppliedSetDiscountData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _AppliedSetDiscountData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AppliedSetDiscountData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _AppliedSetDiscountData value)  $default,){
final _that = this;
switch (_that) {
case _AppliedSetDiscountData():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _AppliedSetDiscountData value)?  $default,){
final _that = this;
switch (_that) {
case _AppliedSetDiscountData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int setDiscountId,  String setDiscountName,  int discountAmount,  int appliedCount,  SetDiscountConditionType conditionType)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AppliedSetDiscountData() when $default != null:
return $default(_that.setDiscountId,_that.setDiscountName,_that.discountAmount,_that.appliedCount,_that.conditionType);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int setDiscountId,  String setDiscountName,  int discountAmount,  int appliedCount,  SetDiscountConditionType conditionType)  $default,) {final _that = this;
switch (_that) {
case _AppliedSetDiscountData():
return $default(_that.setDiscountId,_that.setDiscountName,_that.discountAmount,_that.appliedCount,_that.conditionType);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int setDiscountId,  String setDiscountName,  int discountAmount,  int appliedCount,  SetDiscountConditionType conditionType)?  $default,) {final _that = this;
switch (_that) {
case _AppliedSetDiscountData() when $default != null:
return $default(_that.setDiscountId,_that.setDiscountName,_that.discountAmount,_that.appliedCount,_that.conditionType);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _AppliedSetDiscountData implements AppliedSetDiscountData {
  const _AppliedSetDiscountData({required this.setDiscountId, required this.setDiscountName, required this.discountAmount, required this.appliedCount, required this.conditionType});
  factory _AppliedSetDiscountData.fromJson(Map<String, dynamic> json) => _$AppliedSetDiscountDataFromJson(json);

@override final  int setDiscountId;
// 세트 할인 ID 추가
@override final  String setDiscountName;
@override final  int discountAmount;
@override final  int appliedCount;
@override final  SetDiscountConditionType conditionType;

/// Create a copy of AppliedSetDiscountData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AppliedSetDiscountDataCopyWith<_AppliedSetDiscountData> get copyWith => __$AppliedSetDiscountDataCopyWithImpl<_AppliedSetDiscountData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AppliedSetDiscountDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AppliedSetDiscountData&&(identical(other.setDiscountId, setDiscountId) || other.setDiscountId == setDiscountId)&&(identical(other.setDiscountName, setDiscountName) || other.setDiscountName == setDiscountName)&&(identical(other.discountAmount, discountAmount) || other.discountAmount == discountAmount)&&(identical(other.appliedCount, appliedCount) || other.appliedCount == appliedCount)&&(identical(other.conditionType, conditionType) || other.conditionType == conditionType));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,setDiscountId,setDiscountName,discountAmount,appliedCount,conditionType);

@override
String toString() {
  return 'AppliedSetDiscountData(setDiscountId: $setDiscountId, setDiscountName: $setDiscountName, discountAmount: $discountAmount, appliedCount: $appliedCount, conditionType: $conditionType)';
}


}

/// @nodoc
abstract mixin class _$AppliedSetDiscountDataCopyWith<$Res> implements $AppliedSetDiscountDataCopyWith<$Res> {
  factory _$AppliedSetDiscountDataCopyWith(_AppliedSetDiscountData value, $Res Function(_AppliedSetDiscountData) _then) = __$AppliedSetDiscountDataCopyWithImpl;
@override @useResult
$Res call({
 int setDiscountId, String setDiscountName, int discountAmount, int appliedCount, SetDiscountConditionType conditionType
});




}
/// @nodoc
class __$AppliedSetDiscountDataCopyWithImpl<$Res>
    implements _$AppliedSetDiscountDataCopyWith<$Res> {
  __$AppliedSetDiscountDataCopyWithImpl(this._self, this._then);

  final _AppliedSetDiscountData _self;
  final $Res Function(_AppliedSetDiscountData) _then;

/// Create a copy of AppliedSetDiscountData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? setDiscountId = null,Object? setDiscountName = null,Object? discountAmount = null,Object? appliedCount = null,Object? conditionType = null,}) {
  return _then(_AppliedSetDiscountData(
setDiscountId: null == setDiscountId ? _self.setDiscountId : setDiscountId // ignore: cast_nullable_to_non_nullable
as int,setDiscountName: null == setDiscountName ? _self.setDiscountName : setDiscountName // ignore: cast_nullable_to_non_nullable
as String,discountAmount: null == discountAmount ? _self.discountAmount : discountAmount // ignore: cast_nullable_to_non_nullable
as int,appliedCount: null == appliedCount ? _self.appliedCount : appliedCount // ignore: cast_nullable_to_non_nullable
as int,conditionType: null == conditionType ? _self.conditionType : conditionType // ignore: cast_nullable_to_non_nullable
as SetDiscountConditionType,
  ));
}


}

// dart format on
