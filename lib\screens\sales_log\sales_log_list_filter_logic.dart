import 'package:flutter/material.dart';
import '../../models/sales_log.dart';
import '../../models/sales_log_display_item.dart';
import '../../models/transaction_type.dart';

/// 판매 기록 목록의 필터링 로직을 담당하는 클래스
///
/// 주요 기능:
/// - 판매자별 필터링
/// - 거래 유형별 필터링
/// - 날짜 범위 필터링
/// - 복합 필터링 (여러 조건 조합)
class SalesLogListFilterLogic {
  /// 필터링된 표시 아이템 목록 생성
  ///
  /// [allDisplayItems]: 모든 표시 아이템
  /// [selectedSeller]: 선택된 판매자
  /// [selectedTransactionType]: 선택된 거래 유형 (하위 호환성)
  /// [selectedTransactionTypes]: 선택된 거래 유형들 (다중 선택)
  /// [selectedDateRange]: 선택된 날짜 범위
  /// 반환값: 필터링된 표시 아이템 목록
  static List<SalesLogDisplayItem> getFilteredDisplayItems({
    required List<SalesLogDisplayItem> allDisplayItems,
    required String selectedSeller,
    TransactionType? selectedTransactionType, // 하위 호환성
    Set<TransactionType>? selectedTransactionTypes, // 다중 선택 지원
    required DateTimeRange? selectedDateRange,
  }) {
    return allDisplayItems.where((displayItem) {
      // 판매자 필터 (그룹의 경우 내부 상품 중 하나라도 매치되면 표시)
      if (selectedSeller != '전체 판매자') {
        bool matchesSeller = false;

        if (displayItem is SingleItem) {
          matchesSeller =
              (displayItem.salesLog.sellerName ?? '알 수 없음') == selectedSeller;
        } else if (displayItem is GroupedSale) {
          // 그룹 내 하나라도 해당 판매자의 상품이 있는지 확인
          matchesSeller = displayItem.items.any(
            (log) => (log.sellerName ?? '알 수 없음') == selectedSeller,
          );
        }

        if (!matchesSeller) {
          return false;
        }
      }

      // 거래 유형 필터 (다중 선택 지원)
      Set<TransactionType> effectiveTransactionTypes = selectedTransactionTypes ?? {};
      if (selectedTransactionType != null) {
        effectiveTransactionTypes = {selectedTransactionType}; // 하위 호환성
      }

      if (effectiveTransactionTypes.isNotEmpty) {
        if (displayItem is SingleItem) {
          if (!effectiveTransactionTypes.contains(displayItem.salesLog.transactionType)) {
            return false;
          }
        } else if (displayItem is GroupedSale) {
          // 그룹 내 아이템 중 하나라도 선택된 거래 유형과 일치하는지 확인
          if (!displayItem.items.any(
            (log) => effectiveTransactionTypes.contains(log.transactionType),
          )) {
            return false;
          }
        }
      }

      // 날짜 범위 필터
      if (selectedDateRange != null) {
        SalesLog representativeLog;

        if (displayItem is SingleItem) {
          representativeLog = displayItem.salesLog;
        } else if (displayItem is GroupedSale) {
          if (displayItem.items.isEmpty) return false;
          representativeLog = displayItem.items.first; // 대표 로그
        } else {
          return false;
        }

        final logDate = DateTime.fromMillisecondsSinceEpoch(
          representativeLog.saleTimestamp,
        );
        final startDate = selectedDateRange.start;
        final endDate = selectedDateRange.end.add(
          const Duration(days: 1),
        ); // 종료일 포함

        if (logDate.isBefore(startDate) || logDate.isAfter(endDate)) {
          return false;
        }
      }

      return true;
    }).toList(); // 이미 provider에서 정렬됨
  }

  /// 판매자별 필터링
  ///
  /// [displayItems]: 필터링할 표시 아이템 목록
  /// [selectedSeller]: 선택된 판매자
  /// 반환값: 판매자별로 필터링된 아이템 목록
  static List<SalesLogDisplayItem> filterBySeller({
    required List<SalesLogDisplayItem> displayItems,
    required String selectedSeller,
  }) {
    if (selectedSeller == '전체 판매자') {
      return displayItems;
    }

    return displayItems.where((displayItem) {
      if (displayItem is SingleItem) {
        return (displayItem.salesLog.sellerName ?? '알 수 없음') == selectedSeller;
      } else if (displayItem is GroupedSale) {
        return displayItem.items.any(
          (log) => (log.sellerName ?? '알 수 없음') == selectedSeller,
        );
      }
      return false;
    }).toList();
  }

  /// 거래 유형별 필터링
  ///
  /// [displayItems]: 필터링할 표시 아이템 목록
  /// [selectedTransactionType]: 선택된 거래 유형
  /// 반환값: 거래 유형별로 필터링된 아이템 목록
  static List<SalesLogDisplayItem> filterByTransactionType({
    required List<SalesLogDisplayItem> displayItems,
    required TransactionType? selectedTransactionType,
  }) {
    if (selectedTransactionType == null) {
      return displayItems;
    }

    return displayItems.where((displayItem) {
      if (displayItem is SingleItem) {
        return displayItem.salesLog.transactionType == selectedTransactionType;
      } else if (displayItem is GroupedSale) {
        return displayItem.items.any(
          (log) => log.transactionType == selectedTransactionType,
        );
      }
      return false;
    }).toList();
  }

  /// 날짜 범위별 필터링
  ///
  /// [displayItems]: 필터링할 표시 아이템 목록
  /// [selectedDateRange]: 선택된 날짜 범위
  /// 반환값: 날짜 범위별로 필터링된 아이템 목록
  static List<SalesLogDisplayItem> filterByDateRange({
    required List<SalesLogDisplayItem> displayItems,
    required DateTimeRange? selectedDateRange,
  }) {
    if (selectedDateRange == null) {
      return displayItems;
    }

    final startDate = selectedDateRange.start;
    final endDate = selectedDateRange.end.add(const Duration(days: 1));

    return displayItems.where((displayItem) {
      SalesLog representativeLog;

      if (displayItem is SingleItem) {
        representativeLog = displayItem.salesLog;
      } else if (displayItem is GroupedSale) {
        if (displayItem.items.isEmpty) return false;
        representativeLog = displayItem.items.first;
      } else {
        return false;
      }

      final logDate = DateTime.fromMillisecondsSinceEpoch(
        representativeLog.saleTimestamp,
      );

      return logDate.isAfter(startDate.subtract(const Duration(seconds: 1))) &&
          logDate.isBefore(endDate);
    }).toList();
  }

  /// 필터 조건이 적용되었는지 확인
  ///
  /// [selectedSeller]: 선택된 판매자
  /// [selectedTransactionType]: 선택된 거래 유형
  /// [selectedDateRange]: 선택된 날짜 범위
  /// 반환값: 필터가 적용되었는지 여부
  static bool hasActiveFilters({
    required String selectedSeller,
    required TransactionType? selectedTransactionType,
    required DateTimeRange? selectedDateRange,
  }) {
    return selectedSeller != '전체 판매자' ||
        selectedTransactionType != null ||
        selectedDateRange != null;
  }

  /// 필터 조건을 문자열로 표현
  ///
  /// [selectedSeller]: 선택된 판매자
  /// [selectedTransactionType]: 선택된 거래 유형
  /// [selectedDateRange]: 선택된 날짜 범위
  /// 반환값: 필터 조건 문자열
  static String getFilterDescription({
    required String selectedSeller,
    required TransactionType? selectedTransactionType,
    required DateTimeRange? selectedDateRange,
  }) {
    final filters = <String>[];

    if (selectedSeller != '전체 판매자') {
      filters.add('판매자: $selectedSeller');
    }

    if (selectedTransactionType != null) {
      filters.add('거래유형: ${selectedTransactionType.displayName}');
    }

    if (selectedDateRange != null) {
      final startDate = selectedDateRange.start;
      final endDate = selectedDateRange.end;
      filters.add(
        '기간: ${startDate.year}-${startDate.month.toString().padLeft(2, '0')}-${startDate.day.toString().padLeft(2, '0')} ~ ${endDate.year}-${endDate.month.toString().padLeft(2, '0')}-${endDate.day.toString().padLeft(2, '0')}',
      );
    }

    return filters.isEmpty ? '필터 없음' : filters.join(', ');
  }
} 
