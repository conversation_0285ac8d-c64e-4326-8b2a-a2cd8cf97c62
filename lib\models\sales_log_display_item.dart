import 'sales_log.dart';

/// 원본 SalesLogDisplayItem과 동일한 구조
/// 개별 판매와 그룹 판매(다중판매)를 구분해서 표시
abstract class SalesLogDisplayItem {
  /// 대표 타임스탬프 반환
  int representativeTimestampMillis();

  /// 대표 총액 반환
  int representativeTotalAmount();

  /// 할인이 차감된 실제 판매 금액 반환
  int representativeActualAmount();

  /// 대표 상품명 반환
  String representativeProductNameString();
}

/// 개별 판매 아이템 (batchSaleId가 null인 경우)
class SingleItem extends SalesLogDisplayItem {
  final SalesLog salesLog;

  SingleItem(this.salesLog);

  @override
  int representativeTimestampMillis() => salesLog.saleTimestamp;

  @override
  int representativeTotalAmount() => salesLog.totalAmount;

  @override
  int representativeActualAmount() => salesLog.totalAmount - salesLog.setDiscountAmount - salesLog.manualDiscountAmount;

  @override
  String representativeProductNameString() => salesLog.productName;
}

/// 그룹 판매 아이템 (같은 batchSaleId를 가진 여러 판매)
class GroupedSale extends SalesLogDisplayItem {
  final List<SalesLog> items;
  final String batchId;

  GroupedSale(this.items, this.batchId) {
    // 타임스탬프 기준 내림차순 정렬 (원본과 동일)
    items.sort((a, b) => b.saleTimestamp.compareTo(a.saleTimestamp));
  }

  @override
  int representativeTimestampMillis() =>
      items.isNotEmpty ? items.first.saleTimestamp : 0;

  @override
  int representativeTotalAmount() =>
      items.fold(0, (sum, item) => sum + item.totalAmount);

  @override
  int representativeActualAmount() =>
      items.fold(0, (sum, item) => sum + (item.totalAmount - item.setDiscountAmount - item.manualDiscountAmount));

  @override
  String representativeProductNameString() =>
      items.isNotEmpty ? items.first.productName : '';

  /// 총 수량
  int get totalQuantity =>
      items.fold(0, (sum, item) => sum + item.soldQuantity);

  /// 총 그룹 금액
  int get totalGroupAmount => representativeTotalAmount();

  /// 판매자 목록 (중복 제거)
  List<String> get sellerNames {
    final names = items.map((item) => item.sellerName ?? '알 수 없음').toSet().toList();
    names.sort();
    return names;
  }

  /// 판매자 수
  int get sellerCount => sellerNames.length;

  /// 단일 판매자인지 확인
  bool get isSingleSeller => sellerCount <= 1;

  /// 판매자 표시 텍스트 생성
  String get sellerDisplayText {
    if (isSingleSeller) {
      return sellerNames.first;
    } else {
      return '${sellerNames.first} 외 ${sellerCount - 1}명';
    }
  }

  /// 가장 이름순이 빠른 상품명 (최대 11글자)
  String get representativeProductNameForDisplay {
    if (items.isEmpty) return '';

    // 모든 상품명을 수집하고 정렬
    final productNames = items.map((item) => item.productName).toSet().toList();
    productNames.sort();

    final representativeName = productNames.first;

    // 11글자 제한 적용
    if (representativeName.length <= 11) {
      return representativeName;
    } else {
      return '${representativeName.substring(0, 11)}..';
    }
  }

  /// 묶음 판매 제목 생성 (카테고리명 포함)
  String groupTitleWithCategory(Map<int, String>? productCategoryMap) {
    final itemCount = items.length;

    if (items.isEmpty) return '';

    // 첫 번째 아이템의 카테고리명-상품명 형식 생성 (저장된 카테고리명 우선 사용)
    final firstItem = items.first;
    String displayName = firstItem.productName;

    // 1. 저장된 카테고리명이 있으면 우선 사용
    if (firstItem.categoryName != null && firstItem.categoryName!.isNotEmpty) {
      displayName = '${firstItem.categoryName}-${firstItem.productName}';
    }
    // 2. 저장된 카테고리명이 없으면 현재 상품-카테고리 매핑에서 찾기 (하위 호환성)
    else if (productCategoryMap != null && firstItem.productId != null) {
      final categoryName = productCategoryMap[firstItem.productId];
      if (categoryName != null) {
        displayName = '$categoryName-${firstItem.productName}';
      }
    }

    // 11글자 제한 적용 (카테고리명 포함)
    if (displayName.length > 11) {
      displayName = '${displayName.substring(0, 11)}..';
    }

    if (itemCount == 1) {
      return displayName;
    } else {
      return '$displayName 외 ${itemCount - 1}종류';
    }
  }

  /// 묶음 판매 제목 생성 (하위 호환성을 위해 유지)
  String get groupTitle {
    final itemCount = items.length;
    if (itemCount == 1) {
      return representativeProductNameForDisplay;
    } else {
      return '$representativeProductNameForDisplay 외 ${itemCount - 1}종류';
    }
  }

  /// 총 세트 할인 금액
  int get totalSetDiscountAmount =>
      items.fold(0, (sum, item) => sum + item.setDiscountAmount);

  /// 총 수동 할인 금액
  int get totalManualDiscountAmount =>
      items.fold(0, (sum, item) => sum + item.manualDiscountAmount);

  /// 세트 할인이 적용되었는지 확인
  bool get hasSetDiscount => totalSetDiscountAmount > 0;

  /// 수동 할인이 적용되었는지 확인
  bool get hasManualDiscount => totalManualDiscountAmount > 0;

  /// 적용된 세트 할인 이름들 (중복 제거)
  List<String> get setDiscountNames {
    final names = <String>{};
    for (final item in items) {
      if (item.setDiscountNames != null && item.setDiscountNames!.isNotEmpty) {
        // 쉼표로 구분된 세트 이름들을 분리
        final itemNames = item.setDiscountNames!.split(', ');
        names.addAll(itemNames.map((name) => name.trim()));
      }
    }
    return names.toList()..sort();
  }

  /// 세트 할인 표시 텍스트
  String get setDiscountDisplayText {
    if (!hasSetDiscount) return '';

    final names = setDiscountNames;
    if (names.isEmpty) return '';

    if (names.length == 1) {
      return names.first;
    } else {
      return '${names.first} 외 ${names.length - 1}개';
    }
  }
}
