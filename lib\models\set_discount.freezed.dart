// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'set_discount.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SetDiscount {

 int? get id; String get name; int get discountAmount;// 할인 조건 타입
 SetDiscountConditionType get conditionType;// 기존 상품 조합 조건 (conditionType이 productCombination일 때 사용)
 List<int> get productIds;// 최소 구매 금액 조건 (conditionType이 minimumAmount일 때 사용)
 int get minimumAmount;// 최소 구매 금액 기준 타입 (conditionType이 minimumAmount일 때 사용)
 MinimumAmountBasisType get minimumAmountBasisType;// 카테고리별 수량 조건 (conditionType이 categoryQuantity일 때 사용)
 CategoryQuantityCondition? get categoryCondition;// 상품군 수량 조건 (conditionType이 productGroupQuantity일 때 사용)
 ProductGroupQuantityCondition? get productGroupCondition;// 반복 적용 여부 (카테고리별/상품군별 할인에서 조건을 만족할 때마다 반복 적용할지 여부)
 bool get allowMultipleApplications; bool get isActive; DateTime get createdAt; DateTime? get updatedAt; int get eventId;
/// Create a copy of SetDiscount
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SetDiscountCopyWith<SetDiscount> get copyWith => _$SetDiscountCopyWithImpl<SetDiscount>(this as SetDiscount, _$identity);

  /// Serializes this SetDiscount to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SetDiscount&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.discountAmount, discountAmount) || other.discountAmount == discountAmount)&&(identical(other.conditionType, conditionType) || other.conditionType == conditionType)&&const DeepCollectionEquality().equals(other.productIds, productIds)&&(identical(other.minimumAmount, minimumAmount) || other.minimumAmount == minimumAmount)&&(identical(other.minimumAmountBasisType, minimumAmountBasisType) || other.minimumAmountBasisType == minimumAmountBasisType)&&(identical(other.categoryCondition, categoryCondition) || other.categoryCondition == categoryCondition)&&(identical(other.productGroupCondition, productGroupCondition) || other.productGroupCondition == productGroupCondition)&&(identical(other.allowMultipleApplications, allowMultipleApplications) || other.allowMultipleApplications == allowMultipleApplications)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.eventId, eventId) || other.eventId == eventId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,discountAmount,conditionType,const DeepCollectionEquality().hash(productIds),minimumAmount,minimumAmountBasisType,categoryCondition,productGroupCondition,allowMultipleApplications,isActive,createdAt,updatedAt,eventId);

@override
String toString() {
  return 'SetDiscount(id: $id, name: $name, discountAmount: $discountAmount, conditionType: $conditionType, productIds: $productIds, minimumAmount: $minimumAmount, minimumAmountBasisType: $minimumAmountBasisType, categoryCondition: $categoryCondition, productGroupCondition: $productGroupCondition, allowMultipleApplications: $allowMultipleApplications, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt, eventId: $eventId)';
}


}

/// @nodoc
abstract mixin class $SetDiscountCopyWith<$Res>  {
  factory $SetDiscountCopyWith(SetDiscount value, $Res Function(SetDiscount) _then) = _$SetDiscountCopyWithImpl;
@useResult
$Res call({
 int? id, String name, int discountAmount, SetDiscountConditionType conditionType, List<int> productIds, int minimumAmount, MinimumAmountBasisType minimumAmountBasisType, CategoryQuantityCondition? categoryCondition, ProductGroupQuantityCondition? productGroupCondition, bool allowMultipleApplications, bool isActive, DateTime createdAt, DateTime? updatedAt, int eventId
});




}
/// @nodoc
class _$SetDiscountCopyWithImpl<$Res>
    implements $SetDiscountCopyWith<$Res> {
  _$SetDiscountCopyWithImpl(this._self, this._then);

  final SetDiscount _self;
  final $Res Function(SetDiscount) _then;

/// Create a copy of SetDiscount
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? name = null,Object? discountAmount = null,Object? conditionType = null,Object? productIds = null,Object? minimumAmount = null,Object? minimumAmountBasisType = null,Object? categoryCondition = freezed,Object? productGroupCondition = freezed,Object? allowMultipleApplications = null,Object? isActive = null,Object? createdAt = null,Object? updatedAt = freezed,Object? eventId = null,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,discountAmount: null == discountAmount ? _self.discountAmount : discountAmount // ignore: cast_nullable_to_non_nullable
as int,conditionType: null == conditionType ? _self.conditionType : conditionType // ignore: cast_nullable_to_non_nullable
as SetDiscountConditionType,productIds: null == productIds ? _self.productIds : productIds // ignore: cast_nullable_to_non_nullable
as List<int>,minimumAmount: null == minimumAmount ? _self.minimumAmount : minimumAmount // ignore: cast_nullable_to_non_nullable
as int,minimumAmountBasisType: null == minimumAmountBasisType ? _self.minimumAmountBasisType : minimumAmountBasisType // ignore: cast_nullable_to_non_nullable
as MinimumAmountBasisType,categoryCondition: freezed == categoryCondition ? _self.categoryCondition : categoryCondition // ignore: cast_nullable_to_non_nullable
as CategoryQuantityCondition?,productGroupCondition: freezed == productGroupCondition ? _self.productGroupCondition : productGroupCondition // ignore: cast_nullable_to_non_nullable
as ProductGroupQuantityCondition?,allowMultipleApplications: null == allowMultipleApplications ? _self.allowMultipleApplications : allowMultipleApplications // ignore: cast_nullable_to_non_nullable
as bool,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,eventId: null == eventId ? _self.eventId : eventId // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [SetDiscount].
extension SetDiscountPatterns on SetDiscount {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SetDiscount value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SetDiscount() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SetDiscount value)  $default,){
final _that = this;
switch (_that) {
case _SetDiscount():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SetDiscount value)?  $default,){
final _that = this;
switch (_that) {
case _SetDiscount() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int? id,  String name,  int discountAmount,  SetDiscountConditionType conditionType,  List<int> productIds,  int minimumAmount,  MinimumAmountBasisType minimumAmountBasisType,  CategoryQuantityCondition? categoryCondition,  ProductGroupQuantityCondition? productGroupCondition,  bool allowMultipleApplications,  bool isActive,  DateTime createdAt,  DateTime? updatedAt,  int eventId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SetDiscount() when $default != null:
return $default(_that.id,_that.name,_that.discountAmount,_that.conditionType,_that.productIds,_that.minimumAmount,_that.minimumAmountBasisType,_that.categoryCondition,_that.productGroupCondition,_that.allowMultipleApplications,_that.isActive,_that.createdAt,_that.updatedAt,_that.eventId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int? id,  String name,  int discountAmount,  SetDiscountConditionType conditionType,  List<int> productIds,  int minimumAmount,  MinimumAmountBasisType minimumAmountBasisType,  CategoryQuantityCondition? categoryCondition,  ProductGroupQuantityCondition? productGroupCondition,  bool allowMultipleApplications,  bool isActive,  DateTime createdAt,  DateTime? updatedAt,  int eventId)  $default,) {final _that = this;
switch (_that) {
case _SetDiscount():
return $default(_that.id,_that.name,_that.discountAmount,_that.conditionType,_that.productIds,_that.minimumAmount,_that.minimumAmountBasisType,_that.categoryCondition,_that.productGroupCondition,_that.allowMultipleApplications,_that.isActive,_that.createdAt,_that.updatedAt,_that.eventId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int? id,  String name,  int discountAmount,  SetDiscountConditionType conditionType,  List<int> productIds,  int minimumAmount,  MinimumAmountBasisType minimumAmountBasisType,  CategoryQuantityCondition? categoryCondition,  ProductGroupQuantityCondition? productGroupCondition,  bool allowMultipleApplications,  bool isActive,  DateTime createdAt,  DateTime? updatedAt,  int eventId)?  $default,) {final _that = this;
switch (_that) {
case _SetDiscount() when $default != null:
return $default(_that.id,_that.name,_that.discountAmount,_that.conditionType,_that.productIds,_that.minimumAmount,_that.minimumAmountBasisType,_that.categoryCondition,_that.productGroupCondition,_that.allowMultipleApplications,_that.isActive,_that.createdAt,_that.updatedAt,_that.eventId);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SetDiscount implements SetDiscount {
  const _SetDiscount({this.id, required this.name, required this.discountAmount, this.conditionType = SetDiscountConditionType.productCombination, final  List<int> productIds = const [], this.minimumAmount = 0, this.minimumAmountBasisType = MinimumAmountBasisType.beforeOtherDiscounts, this.categoryCondition, this.productGroupCondition, this.allowMultipleApplications = false, this.isActive = true, required this.createdAt, this.updatedAt, this.eventId = 1}): _productIds = productIds;
  factory _SetDiscount.fromJson(Map<String, dynamic> json) => _$SetDiscountFromJson(json);

@override final  int? id;
@override final  String name;
@override final  int discountAmount;
// 할인 조건 타입
@override@JsonKey() final  SetDiscountConditionType conditionType;
// 기존 상품 조합 조건 (conditionType이 productCombination일 때 사용)
 final  List<int> _productIds;
// 기존 상품 조합 조건 (conditionType이 productCombination일 때 사용)
@override@JsonKey() List<int> get productIds {
  if (_productIds is EqualUnmodifiableListView) return _productIds;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_productIds);
}

// 최소 구매 금액 조건 (conditionType이 minimumAmount일 때 사용)
@override@JsonKey() final  int minimumAmount;
// 최소 구매 금액 기준 타입 (conditionType이 minimumAmount일 때 사용)
@override@JsonKey() final  MinimumAmountBasisType minimumAmountBasisType;
// 카테고리별 수량 조건 (conditionType이 categoryQuantity일 때 사용)
@override final  CategoryQuantityCondition? categoryCondition;
// 상품군 수량 조건 (conditionType이 productGroupQuantity일 때 사용)
@override final  ProductGroupQuantityCondition? productGroupCondition;
// 반복 적용 여부 (카테고리별/상품군별 할인에서 조건을 만족할 때마다 반복 적용할지 여부)
@override@JsonKey() final  bool allowMultipleApplications;
@override@JsonKey() final  bool isActive;
@override final  DateTime createdAt;
@override final  DateTime? updatedAt;
@override@JsonKey() final  int eventId;

/// Create a copy of SetDiscount
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SetDiscountCopyWith<_SetDiscount> get copyWith => __$SetDiscountCopyWithImpl<_SetDiscount>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SetDiscountToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SetDiscount&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.discountAmount, discountAmount) || other.discountAmount == discountAmount)&&(identical(other.conditionType, conditionType) || other.conditionType == conditionType)&&const DeepCollectionEquality().equals(other._productIds, _productIds)&&(identical(other.minimumAmount, minimumAmount) || other.minimumAmount == minimumAmount)&&(identical(other.minimumAmountBasisType, minimumAmountBasisType) || other.minimumAmountBasisType == minimumAmountBasisType)&&(identical(other.categoryCondition, categoryCondition) || other.categoryCondition == categoryCondition)&&(identical(other.productGroupCondition, productGroupCondition) || other.productGroupCondition == productGroupCondition)&&(identical(other.allowMultipleApplications, allowMultipleApplications) || other.allowMultipleApplications == allowMultipleApplications)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.eventId, eventId) || other.eventId == eventId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,discountAmount,conditionType,const DeepCollectionEquality().hash(_productIds),minimumAmount,minimumAmountBasisType,categoryCondition,productGroupCondition,allowMultipleApplications,isActive,createdAt,updatedAt,eventId);

@override
String toString() {
  return 'SetDiscount(id: $id, name: $name, discountAmount: $discountAmount, conditionType: $conditionType, productIds: $productIds, minimumAmount: $minimumAmount, minimumAmountBasisType: $minimumAmountBasisType, categoryCondition: $categoryCondition, productGroupCondition: $productGroupCondition, allowMultipleApplications: $allowMultipleApplications, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt, eventId: $eventId)';
}


}

/// @nodoc
abstract mixin class _$SetDiscountCopyWith<$Res> implements $SetDiscountCopyWith<$Res> {
  factory _$SetDiscountCopyWith(_SetDiscount value, $Res Function(_SetDiscount) _then) = __$SetDiscountCopyWithImpl;
@override @useResult
$Res call({
 int? id, String name, int discountAmount, SetDiscountConditionType conditionType, List<int> productIds, int minimumAmount, MinimumAmountBasisType minimumAmountBasisType, CategoryQuantityCondition? categoryCondition, ProductGroupQuantityCondition? productGroupCondition, bool allowMultipleApplications, bool isActive, DateTime createdAt, DateTime? updatedAt, int eventId
});




}
/// @nodoc
class __$SetDiscountCopyWithImpl<$Res>
    implements _$SetDiscountCopyWith<$Res> {
  __$SetDiscountCopyWithImpl(this._self, this._then);

  final _SetDiscount _self;
  final $Res Function(_SetDiscount) _then;

/// Create a copy of SetDiscount
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? name = null,Object? discountAmount = null,Object? conditionType = null,Object? productIds = null,Object? minimumAmount = null,Object? minimumAmountBasisType = null,Object? categoryCondition = freezed,Object? productGroupCondition = freezed,Object? allowMultipleApplications = null,Object? isActive = null,Object? createdAt = null,Object? updatedAt = freezed,Object? eventId = null,}) {
  return _then(_SetDiscount(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,discountAmount: null == discountAmount ? _self.discountAmount : discountAmount // ignore: cast_nullable_to_non_nullable
as int,conditionType: null == conditionType ? _self.conditionType : conditionType // ignore: cast_nullable_to_non_nullable
as SetDiscountConditionType,productIds: null == productIds ? _self._productIds : productIds // ignore: cast_nullable_to_non_nullable
as List<int>,minimumAmount: null == minimumAmount ? _self.minimumAmount : minimumAmount // ignore: cast_nullable_to_non_nullable
as int,minimumAmountBasisType: null == minimumAmountBasisType ? _self.minimumAmountBasisType : minimumAmountBasisType // ignore: cast_nullable_to_non_nullable
as MinimumAmountBasisType,categoryCondition: freezed == categoryCondition ? _self.categoryCondition : categoryCondition // ignore: cast_nullable_to_non_nullable
as CategoryQuantityCondition?,productGroupCondition: freezed == productGroupCondition ? _self.productGroupCondition : productGroupCondition // ignore: cast_nullable_to_non_nullable
as ProductGroupQuantityCondition?,allowMultipleApplications: null == allowMultipleApplications ? _self.allowMultipleApplications : allowMultipleApplications // ignore: cast_nullable_to_non_nullable
as bool,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,eventId: null == eventId ? _self.eventId : eventId // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

// dart format on
