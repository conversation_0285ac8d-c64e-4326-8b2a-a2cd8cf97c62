enum PrepaymentSortOrder {
  registrationDateDesc('REGISTRATION_DATE_DESC', '등록순 (최신순)'),
  registrationDateAsc('REGISTRATION_DATE_ASC', '등록순 (오래된순)'),
  prepaymentNumberAsc('PREPAYMENT_NUMBER_ASC', '선입금 번호 오름차순'),
  prepaymentNumberDesc('PREPAYMENT_NUMBER_DESC', '선입금 번호 내림차순'),
  buyerNameAsc('BUYER_NAME_ASC', '이름순'),
  buyerNameDesc('BUYER_NAME_DESC', '이름순'),
  amountAsc('AMOUNT_ASC', '금액 오름차순'),
  amountDesc('AMOUNT_DESC', '금액 내림차순');

  const PrepaymentSortOrder(this.value, this.displayName);

  final String value;
  final String displayName;

  // 이름순 정렬인지 확인
  bool get isNameSort => this == PrepaymentSortOrder.buyerNameAsc || this == PrepaymentSortOrder.buyerNameDesc;
  
  // 오름차순인지 확인
  bool get isAscending => this == PrepaymentSortOrder.registrationDateAsc ||
                         this == PrepaymentSortOrder.prepaymentNumberAsc ||
                         this == PrepaymentSortOrder.buyerNameAsc ||
                         this == PrepaymentSortOrder.amountAsc;

  // 이름순 정렬 토글
  PrepaymentSortOrder toggleNameSort() {
    if (this == PrepaymentSortOrder.buyerNameAsc) {
      return PrepaymentSortOrder.buyerNameDesc;
    } else if (this == PrepaymentSortOrder.buyerNameDesc) {
      return PrepaymentSortOrder.buyerNameAsc;
    }
    return PrepaymentSortOrder.buyerNameAsc; // 기본값
  }

  static List<String> get displayNames {
    return PrepaymentSortOrder.values.map((e) => e.displayName).toList();
  }

  static PrepaymentSortOrder? fromDisplayName(String displayName) {
    for (PrepaymentSortOrder order in PrepaymentSortOrder.values) {
      if (order.displayName == displayName) {
        return order;
      }
    }
    return null;
  }

  @override
  String toString() => value;
}

// 정렬 UI용 통합 옵션 (ProductSortType과 동일한 패턴)
enum PrepaymentSortType {
  registrationDate('등록순으로 정렬'),
  prepaymentNumber('선입금 번호로 정렬'),
  buyerName('구매자명으로 정렬'),
  amount('금액으로 정렬');

  const PrepaymentSortType(this.displayName);

  final String displayName;

  // 현재 정렬 방향을 반전시킨 옵션 반환
  PrepaymentSortOrder getOrder(bool isAscending) {
    switch (this) {
      case PrepaymentSortType.registrationDate:
        return isAscending
            ? PrepaymentSortOrder.registrationDateAsc
            : PrepaymentSortOrder.registrationDateDesc;
      case PrepaymentSortType.prepaymentNumber:
        return isAscending
            ? PrepaymentSortOrder.prepaymentNumberAsc
            : PrepaymentSortOrder.prepaymentNumberDesc;
      case PrepaymentSortType.buyerName:
        return isAscending
            ? PrepaymentSortOrder.buyerNameAsc
            : PrepaymentSortOrder.buyerNameDesc;
      case PrepaymentSortType.amount:
        return isAscending
            ? PrepaymentSortOrder.amountAsc
            : PrepaymentSortOrder.amountDesc;
    }
  }

  // 현재 PrepaymentSortOrder에서 SortType과 방향을 추출
  static PrepaymentSortType? fromOrder(PrepaymentSortOrder order) {
    switch (order) {
      case PrepaymentSortOrder.registrationDateAsc:
      case PrepaymentSortOrder.registrationDateDesc:
        return PrepaymentSortType.registrationDate;
      case PrepaymentSortOrder.prepaymentNumberAsc:
      case PrepaymentSortOrder.prepaymentNumberDesc:
        return PrepaymentSortType.prepaymentNumber;
      case PrepaymentSortOrder.buyerNameAsc:
      case PrepaymentSortOrder.buyerNameDesc:
        return PrepaymentSortType.buyerName;
      case PrepaymentSortOrder.amountAsc:
      case PrepaymentSortOrder.amountDesc:
        return PrepaymentSortType.amount;
    }
  }

  static bool isAscending(PrepaymentSortOrder order) {
    switch (order) {
      case PrepaymentSortOrder.registrationDateAsc:
      case PrepaymentSortOrder.prepaymentNumberAsc:
      case PrepaymentSortOrder.buyerNameAsc:
      case PrepaymentSortOrder.amountAsc:
        return true;
      case PrepaymentSortOrder.registrationDateDesc:
      case PrepaymentSortOrder.prepaymentNumberDesc:
      case PrepaymentSortOrder.buyerNameDesc:
      case PrepaymentSortOrder.amountDesc:
        return false;
    }
  }
}
