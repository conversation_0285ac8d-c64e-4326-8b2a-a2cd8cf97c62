/// 바라 부스 매니저 - 인앱 구독 서비스
///
/// iOS App Store와 Google Play Store의 인앱 구독을 관리하는 서비스입니다.
/// - 플랫폼별 인앱 구독 상품 관리
/// - 구독 상태 확인 및 복원
/// - Firebase와 구독 상태 동기화
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 1월
library;

import 'dart:async';
import 'dart:io';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/logger_utils.dart';
import '../models/subscription_plan.dart';
import 'subscription_service.dart';
import '../providers/subscription_provider.dart';

/// 인앱 구독 서비스
class InAppPurchaseService {
  static const String _tag = 'InAppPurchaseService';

  // 인앱 구독 상품 ID들
  static const String _plusPlanProductId = 'barabooth_plus_monthly';

  // Firebase 인스턴스
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // InAppPurchase 인스턴스
  final InAppPurchase _inAppPurchase = InAppPurchase.instance;

  // 구독 상태 스트림
  late StreamSubscription<List<PurchaseDetails>> _subscription;

  // Riverpod Ref for provider invalidation
  WidgetRef? _ref;

  /// 싱글톤 인스턴스
  static final InAppPurchaseService _instance = InAppPurchaseService._internal();
  factory InAppPurchaseService() => _instance;
  InAppPurchaseService._internal();

  /// Riverpod Ref 설정 (Provider 갱신을 위해)
  void setRef(WidgetRef ref) {
    _ref = ref;
  }

  /// 현재 로그인된 사용자 ID 가져오기
  String? get _currentUserId => _auth.currentUser?.uid;

  /// 서비스 초기화
  Future<void> initialize() async {
    try {
      LoggerUtils.logInfo('인앱 구독 서비스 초기화 시작', tag: _tag);
      
      // 인앱 구매 사용 가능 여부 확인
      final bool available = await _inAppPurchase.isAvailable();
      if (!available) {
        LoggerUtils.logError('인앱 구매를 사용할 수 없습니다', tag: _tag);
        return;
      }
      
      // 플랫폼별 설정
      if (Platform.isAndroid) {
        // Android 특정 설정 (enablePendingPurchases는 더 이상 필요하지 않음)
        LoggerUtils.logInfo('Android 플랫폼 감지됨', tag: _tag);
      }
      
      // 구매 상태 변경 리스너 설정
      _subscription = _inAppPurchase.purchaseStream.listen(
        _onPurchaseUpdated,
        onDone: () => LoggerUtils.logInfo('구매 스트림 종료', tag: _tag),
        onError: (error) => LoggerUtils.logError('구매 스트림 오류', tag: _tag, error: error),
      );
      
      LoggerUtils.logInfo('인앱 구독 서비스 초기화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('인앱 구독 서비스 초기화 실패', tag: _tag, error: e);
    }
  }

  /// 서비스 종료
  void dispose() {
    _subscription.cancel();
  }

  /// 사용 가능한 구독 상품 조회
  Future<List<ProductDetails>> getAvailableProducts() async {
    try {
      LoggerUtils.logInfo('사용 가능한 구독 상품 조회 시작', tag: _tag);
      
      const Set<String> productIds = {_plusPlanProductId};
      final ProductDetailsResponse response = await _inAppPurchase.queryProductDetails(productIds);
      
      if (response.error != null) {
        LoggerUtils.logError('상품 조회 실패', tag: _tag, error: response.error);
        return [];
      }
      
      LoggerUtils.logInfo('조회된 상품 수: ${response.productDetails.length}', tag: _tag);
      return response.productDetails;
    } catch (e) {
      LoggerUtils.logError('상품 조회 중 오류 발생', tag: _tag, error: e);
      return [];
    }
  }

  /// 플러스 플랜 구독 시작
  Future<bool> subscribeToPlusPlan() async {
    try {
      if (_currentUserId == null) {
        LoggerUtils.logError('로그인이 필요합니다', tag: _tag);
        return false;
      }
      
      LoggerUtils.logInfo('플러스 플랜 구독 시작', tag: _tag);
      
      // 상품 정보 조회
      final products = await getAvailableProducts();
      final plusProduct = products.where((product) => product.id == _plusPlanProductId).firstOrNull;
      
      if (plusProduct == null) {
        LoggerUtils.logError('플러스 플랜 상품을 찾을 수 없습니다', tag: _tag);
        return false;
      }
      
      // 구매 요청 (구독의 경우 buyNonConsumable 사용)
      final PurchaseParam purchaseParam = PurchaseParam(productDetails: plusProduct);
      final bool success = await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
      
      LoggerUtils.logInfo('구매 요청 결과: $success', tag: _tag);
      return success;
    } catch (e) {
      LoggerUtils.logError('플러스 플랜 구독 실패', tag: _tag, error: e);
      return false;
    }
  }

  /// 구독 복원
  Future<void> restorePurchases() async {
    try {
      LoggerUtils.logInfo('구독 복원 시작', tag: _tag);
      await _inAppPurchase.restorePurchases();
      LoggerUtils.logInfo('구독 복원 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('구독 복원 실패', tag: _tag, error: e);
    }
  }

  /// 현재 활성 구독 조회
  Future<List<PurchaseDetails>> getActiveSubscriptions() async {
    try {
      LoggerUtils.logInfo('활성 구독 조회 시작', tag: _tag);
      
      // 과거 구매 내역 조회
      await _inAppPurchase.restorePurchases();
      
      // 현재 활성 구독만 필터링하여 반환
      // 실제 구현에서는 구매 상태를 확인하여 활성 구독만 반환해야 함
      return [];
    } catch (e) {
      LoggerUtils.logError('활성 구독 조회 실패', tag: _tag, error: e);
      return [];
    }
  }

  /// 구매 상태 변경 처리
  void _onPurchaseUpdated(List<PurchaseDetails> purchaseDetailsList) async {
    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      LoggerUtils.logInfo('구매 상태 변경: ${purchaseDetails.status}', tag: _tag);
      
      switch (purchaseDetails.status) {
        case PurchaseStatus.pending:
          LoggerUtils.logInfo('구매 대기 중', tag: _tag);
          break;
        case PurchaseStatus.purchased:
          await _handleSuccessfulPurchase(purchaseDetails);
          break;
        case PurchaseStatus.error:
          LoggerUtils.logError('구매 실패', tag: _tag, error: purchaseDetails.error);
          break;
        case PurchaseStatus.restored:
          await _handleRestoredPurchase(purchaseDetails);
          break;
        case PurchaseStatus.canceled:
          LoggerUtils.logInfo('구매 취소됨', tag: _tag);
          break;
      }
      
      // 구매 완료 처리
      if (purchaseDetails.pendingCompletePurchase) {
        await _inAppPurchase.completePurchase(purchaseDetails);
      }
    }
  }

  /// 성공한 구매 처리
  Future<void> _handleSuccessfulPurchase(PurchaseDetails purchaseDetails) async {
    try {
      LoggerUtils.logInfo('구매 성공 처리 시작: ${purchaseDetails.productID}', tag: _tag);
      
      if (purchaseDetails.productID == _plusPlanProductId) {
        // 플러스 플랜 구독 활성화
        await _activatePlusSubscription(purchaseDetails);
      }
      
      LoggerUtils.logInfo('구매 성공 처리 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('구매 성공 처리 실패', tag: _tag, error: e);
    }
  }

  /// 복원된 구매 처리 (🔐 보안 강화)
  Future<void> _handleRestoredPurchase(PurchaseDetails purchaseDetails) async {
    try {
      LoggerUtils.logInfo('🔐 보안 강화된 구매 복원 처리 시작: ${purchaseDetails.productID}', tag: _tag);

      if (purchaseDetails.productID == _plusPlanProductId) {
        // 🔐 구독 복원 권한 확인
        final canRestore = await _canRestoreSubscription(purchaseDetails);

        if (canRestore) {
          LoggerUtils.logInfo('🔐 구독 복원 권한 확인됨 - 복원 진행', tag: _tag);
          await _activatePlusSubscription(purchaseDetails);
        } else {
          LoggerUtils.logWarning('🚨 구독 복원 권한 없음 - 다른 계정의 구독입니다', tag: _tag);
          // 복원 실패를 사용자에게 알림 (필요시 UI에서 처리)
        }
      }

      LoggerUtils.logInfo('구매 복원 처리 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('구매 복원 처리 실패', tag: _tag, error: e);
    }
  }

  /// 플러스 플랜 구독 활성화 (🔐 보안 강화)
  Future<void> _activatePlusSubscription(PurchaseDetails purchaseDetails) async {
    try {
      if (_currentUserId == null) {
        LoggerUtils.logError('사용자 ID가 없습니다', tag: _tag);
        return;
      }

      LoggerUtils.logInfo('🔐 보안 강화된 플러스 플랜 구독 활성화 시작', tag: _tag);

      final now = DateTime.now();
      final endDate = DateTime(now.year, now.month + 1, now.day); // 1개월 후

      // 🔐 현재 사용자의 플랫폼 계정 정보 수집
      final platformInfo = await _getCurrentPlatformInfo();

      LoggerUtils.logInfo('🔐 플랫폼 정보: ${platformInfo['platform']}, 계정: ${platformInfo['platformAccountId']}', tag: _tag);

      // 🔐 보안 강화된 구독 정보로 UserSubscription 생성
      final subscription = UserSubscription.createPlus(
        userId: _currentUserId!,
        startDate: now,
        endDate: endDate,
        originalPurchaserUserId: _currentUserId!, // 원본 구매자 = 현재 사용자
        platformAccountId: platformInfo['platformAccountId'],
        platform: platformInfo['platform'],
        purchaseId: purchaseDetails.purchaseID,
        productId: purchaseDetails.productID,
      );

      // Firebase에 보안 강화된 구독 정보 저장
      await _firestore
          .collection('users')
          .doc(_currentUserId!)
          .collection('subscriptions')
          .doc('current')
          .set(subscription.toFirebaseMap());

      // 기존 SubscriptionService도 업데이트
      final subscriptionService = SubscriptionService();
      await subscriptionService.updateSubscriptionPlan(SubscriptionPlanType.plus);

      // 모든 구독 관련 Provider 즉시 갱신
      if (_ref != null) {
        _ref!.invalidate(subscriptionNotifierProvider);
        _ref!.invalidate(currentPlanTypeProvider);
        _ref!.invalidate(isPlusUserProvider);
        _ref!.invalidate(currentSubscriptionProvider);
        _ref!.invalidate(featureAccessProvider('sellerManagement'));
        _ref!.invalidate(featureAccessProvider('serverSync'));
        _ref!.invalidate(featureAccessProvider('setDiscount'));
        _ref!.invalidate(featureAccessProvider('service'));
        _ref!.invalidate(featureAccessProvider('excelExport'));
        _ref!.invalidate(featureAccessProvider('pdfExport'));
        _ref!.invalidate(featureAccessProvider('advancedStats'));
        _ref!.invalidate(hasSellerManagementFeatureProvider);
        _ref!.invalidate(hasServerSyncFeatureProvider);
        _ref!.invalidate(hasSetDiscountFeatureProvider);
        _ref!.invalidate(hasServiceFeatureProvider);
        _ref!.invalidate(hasExcelExportFeatureProvider);
        LoggerUtils.logInfo('모든 구독 관련 Provider 갱신 완료', tag: _tag);
      }

      LoggerUtils.logInfo('🔐 보안 강화된 플러스 플랜 구독 활성화 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('플러스 플랜 구독 활성화 실패', tag: _tag, error: e);
    }
  }

  /// 🔐 현재 사용자의 플랫폼 계정 정보 수집
  Future<Map<String, String?>> _getCurrentPlatformInfo() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        LoggerUtils.logWarning('Firebase 사용자가 없습니다', tag: _tag);
        return {'platform': null, 'platformAccountId': null};
      }

      // 사용자의 provider 정보 확인
      for (final providerData in user.providerData) {
        switch (providerData.providerId) {
          case 'apple.com':
            LoggerUtils.logInfo('🍎 Apple 로그인 사용자 감지', tag: _tag);
            return {
              'platform': 'apple',
              'platformAccountId': providerData.uid, // Apple ID
            };
          case 'google.com':
            LoggerUtils.logInfo('🔍 Google 로그인 사용자 감지', tag: _tag);
            return {
              'platform': 'google',
              'platformAccountId': providerData.uid, // Google Account ID
            };
        }
      }

      // 이메일/패스워드 로그인이거나 알 수 없는 경우
      LoggerUtils.logWarning('알 수 없는 로그인 방법 또는 이메일 로그인', tag: _tag);
      return {
        'platform': Platform.isIOS ? 'apple' : 'google', // 플랫폼 기본값
        'platformAccountId': user.uid, // Firebase UID 사용
      };
    } catch (e) {
      LoggerUtils.logError('플랫폼 정보 수집 실패', tag: _tag, error: e);
      return {
        'platform': Platform.isIOS ? 'apple' : 'google',
        'platformAccountId': null,
      };
    }
  }

  /// 🔐 구독 복원 권한 확인 (크로스 플랫폼 지원)
  Future<bool> _canRestoreSubscription(PurchaseDetails purchaseDetails) async {
    try {
      if (_currentUserId == null) {
        LoggerUtils.logError('사용자 ID가 없습니다', tag: _tag);
        return false;
      }

      // 현재 사용자의 플랫폼 정보 수집
      final currentPlatformInfo = await _getCurrentPlatformInfo();
      final currentPlatform = currentPlatformInfo['platform'];
      final currentPlatformAccountId = currentPlatformInfo['platformAccountId'];

      if (currentPlatform == null || currentPlatformAccountId == null) {
        LoggerUtils.logWarning('현재 사용자의 플랫폼 정보를 확인할 수 없습니다', tag: _tag);
        return false;
      }

      LoggerUtils.logInfo('🔐 구독 복원 권한 확인 중...', tag: _tag);
      LoggerUtils.logInfo('- 현재 플랫폼: $currentPlatform', tag: _tag);
      LoggerUtils.logInfo('- 현재 플랫폼 계정: $currentPlatformAccountId', tag: _tag);
      LoggerUtils.logInfo('- 복원할 구매 ID: ${purchaseDetails.purchaseID}', tag: _tag);

      // Firebase에서 해당 구매 ID로 기존 구독 검색
      final querySnapshot = await _firestore
          .collectionGroup('subscriptions')
          .where('purchaseId', isEqualTo: purchaseDetails.purchaseID)
          .where('productId', isEqualTo: purchaseDetails.productID)
          .limit(1)
          .get();

      if (querySnapshot.docs.isEmpty) {
        LoggerUtils.logInfo('🔐 기존 구독 기록이 없음 - 새로운 구독으로 처리', tag: _tag);
        return true; // 새로운 구독이므로 허용
      }

      // 기존 구독 정보 확인
      final existingSubscription = querySnapshot.docs.first.data();
      final originalPurchaserUserId = existingSubscription['originalPurchaserUserId'] as String?;
      final existingPlatformAccountId = existingSubscription['platformAccountId'] as String?;
      final existingPlatform = existingSubscription['platform'] as String?;

      LoggerUtils.logInfo('🔐 기존 구독 정보 발견:', tag: _tag);
      LoggerUtils.logInfo('- 원본 구매자: $originalPurchaserUserId', tag: _tag);
      LoggerUtils.logInfo('- 기존 플랫폼: $existingPlatform', tag: _tag);
      LoggerUtils.logInfo('- 기존 플랫폼 계정: $existingPlatformAccountId', tag: _tag);

      // 🌟 크로스 플랫폼 지원: 원본 구매자라면 플랫폼이 달라도 허용
      final isOriginalPurchaser = originalPurchaserUserId == _currentUserId;
      final isSamePlatformAccount = existingPlatformAccountId == currentPlatformAccountId &&
                                   existingPlatform == currentPlatform;

      if (isOriginalPurchaser) {
        if (isSamePlatformAccount) {
          LoggerUtils.logInfo('🔐 구독 복원 권한 확인됨 (같은 플랫폼)', tag: _tag);
        } else {
          LoggerUtils.logInfo('🌟 구독 복원 권한 확인됨 (크로스 플랫폼: $existingPlatform → $currentPlatform)', tag: _tag);
        }
        return true;
      } else {
        LoggerUtils.logWarning('🚨 구독 복원 권한 없음 - 다른 사용자의 구독입니다', tag: _tag);
        return false;
      }

    } catch (e) {
      LoggerUtils.logError('구독 복원 권한 확인 실패', tag: _tag, error: e);
      return false; // 오류 시 복원 거부
    }
  }
}
