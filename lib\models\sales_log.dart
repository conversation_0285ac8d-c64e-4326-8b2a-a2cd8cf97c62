import 'package:freezed_annotation/freezed_annotation.dart';
import 'transaction_type.dart';

part 'sales_log.freezed.dart';
part 'sales_log.g.dart';

/// 판매 기록(영수증/로그) 정보를 표현하는 데이터 모델 클래스입니다.
/// - 상품ID, 상품명, 판매자, 판매가격, 수량, 총액, 일시, 거래유형 등 다양한 속성 포함
/// - DB 연동, CRUD, 통계/필터/정렬/검색 등에서 사용
@freezed
abstract class SalesLog with _$SalesLog {
  const factory SalesLog({
    required int id,
    int? productId,
    required String productName,
    String? categoryName, // 카테고리명 추가 (상품 삭제 시에도 유지)
    String? sellerName,
    required int soldPrice,
    required int soldQuantity,
    required int totalAmount,
    required int saleTimestamp,
    @Default(TransactionType.sale) TransactionType transactionType,
    String? batchSaleId,
    @Default(1) int eventId, // 행사 ID 추가
    @Default(0) int setDiscountAmount, // 세트 할인 금액
    String? setDiscountNames, // 적용된 세트 할인 이름들 (JSON 문자열)
    @Default(0) int manualDiscountAmount, // 수동 할인 금액
    @Default(null) String? paymentMethod, // 결제 수단 (전역 설정)
  }) = _SalesLog;

  factory SalesLog.fromJson(Map<String, dynamic> json) => _$SalesLogFromJson(json);

  // SQLite 맵에서 직접 생성
  factory SalesLog.fromMap(Map<String, dynamic> map) {
    return SalesLog(
      id: map['id'],
      productId: map['productId'],
      productName: map['productName'] ?? '',
      categoryName: map['categoryName'] as String?, // 카테고리명 추가
      sellerName: map['sellerName'],
      soldPrice: map['soldPrice'] ?? 0,
      soldQuantity: map['soldQuantity'] ?? 0,
      totalAmount: map['totalAmount'] ?? 0,
      saleTimestamp: map['saleTimestamp'] ?? 0,
      transactionType:
          TransactionType.fromString(map['transactionType']) ??
          TransactionType.sale,
      batchSaleId: map['batchSaleId'],
      eventId: map['eventId'] as int? ?? 1, // 기본값 1
      setDiscountAmount: map['setDiscountAmount'] as int? ?? 0,
      setDiscountNames: map['setDiscountNames'] as String?,
      manualDiscountAmount: map['manualDiscountAmount'] as int? ?? 0,
      paymentMethod: map['paymentMethod'] as String?,
    );
  }

  // 현재 타임스탬프로 생성하는 팩토리
  factory SalesLog.create({
    required int id,
    int? productId,
    required String productName,
    String? categoryName, // 카테고리명 추가
    String? sellerName,
    required int soldPrice,
    required int soldQuantity,
    required int totalAmount,
    TransactionType transactionType = TransactionType.sale,
    String? batchSaleId,
    int eventId = 1, // 행사 ID 추가
    int setDiscountAmount = 0, // 세트 할인 금액
    String? setDiscountNames, // 적용된 세트 할인 이름들
    int manualDiscountAmount = 0, // 수동 할인 금액
  }) {
    return SalesLog(
      id: id,
      productId: productId,
      productName: productName,
      categoryName: categoryName, // 카테고리명 전달
      sellerName: sellerName,
      soldPrice: soldPrice,
      soldQuantity: soldQuantity,
      totalAmount: totalAmount,
      saleTimestamp: DateTime.now().millisecondsSinceEpoch,
      transactionType: transactionType,
      batchSaleId: batchSaleId,
      eventId: eventId,
      setDiscountAmount: setDiscountAmount,
      setDiscountNames: setDiscountNames,
      manualDiscountAmount: manualDiscountAmount,
      paymentMethod: null,
    );
  }
}

// SQLite 맵 변환을 위한 Extension
extension SalesLogMapper on SalesLog {
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'productId': productId,
      'productName': productName,
      'categoryName': categoryName, // 카테고리명 추가
      'sellerName': sellerName,
      'soldPrice': soldPrice,
      'soldQuantity': soldQuantity,
      'totalAmount': totalAmount,
      'saleTimestamp': saleTimestamp,
      'transactionType': transactionType.value,
      'batchSaleId': batchSaleId,
      'eventId': eventId,
      'setDiscountAmount': this.setDiscountAmount,
      'setDiscountNames': this.setDiscountNames,
      'manualDiscountAmount': this.manualDiscountAmount,
      'paymentMethod': this.paymentMethod,
    };
  }

  /// 실제 판매 금액 (할인 적용 후)
  int get actualAmount => totalAmount - setDiscountAmount - manualDiscountAmount;
}
