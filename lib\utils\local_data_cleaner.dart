/// 로컬 데이터 완전 삭제 유틸리티
/// 
/// 로그아웃, 회원탈퇴 시 모든 로컬 데이터를 완전히 삭제하는 기능을 제공합니다.
library;

import 'dart:io';
import 'package:flutter/material.dart';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:sqflite/sqflite.dart';

import '../services/database_service.dart';
import '../services/event_workspace_manager.dart';
import '../providers/nickname_provider.dart';
import '../utils/logger_utils.dart';
import '../utils/image_cache.dart';
import '../utils/image_sync_utils.dart';
import '../utils/state_sync_manager.dart';


class LocalDataCleaner {
  static const String _tag = 'LocalDataCleaner';

  /// 모든 로컬 데이터를 완전히 삭제
  /// 
  /// [includeOnboarding] - 온보딩 상태도 삭제할지 여부 (기본값: true)
  /// [showProgress] - 진행 상황을 로그로 표시할지 여부 (기본값: true)
  static Future<void> clearAllLocalData({
    bool includeOnboarding = true,
    bool showProgress = true,
    WidgetRef? ref,
  }) async {
    try {
      if (showProgress) {
        LoggerUtils.logInfo('🗑️ 모든 로컬 데이터 완전 삭제 시작', tag: _tag);
      }

      // 1. SharedPreferences 완전 삭제
      await _clearSharedPreferences(includeOnboarding: includeOnboarding, showProgress: showProgress);

      // 2. SQLite 데이터베이스 완전 삭제
      await _clearDatabase(ref: ref, showProgress: showProgress);

      // 3. 이미지 캐시 완전 삭제
      await _clearImageCache(showProgress: showProgress);

      // 4. 애플리케이션 문서 디렉토리의 모든 파일 삭제
      await _clearApplicationFiles(showProgress: showProgress);

      // 5. 임시 디렉토리 정리
      await _clearTemporaryFiles(showProgress: showProgress);

      // 6. Flutter 이미지 캐시 정리
      await _clearFlutterImageCache(showProgress: showProgress);

      if (showProgress) {
        LoggerUtils.logInfo('✅ 모든 로컬 데이터 완전 삭제 완료', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('로컬 데이터 완전 삭제 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// SharedPreferences 완전 삭제
  static Future<void> _clearSharedPreferences({
    bool includeOnboarding = true,
    bool showProgress = true,
  }) async {
    try {
      if (showProgress) {
        LoggerUtils.logInfo('1️⃣ SharedPreferences 삭제 중...', tag: _tag);
      }

      final prefs = await SharedPreferences.getInstance();
      
      if (includeOnboarding) {
        // 모든 데이터 삭제
        await prefs.clear();
      } else {
        // 온보딩 상태는 유지하고 나머지만 삭제
        final onboardingState = prefs.getBool('isOnboarded');
        await prefs.clear();
        if (onboardingState != null) {
          await prefs.setBool('isOnboarded', onboardingState);
        }
      }

      if (showProgress) {
        LoggerUtils.logInfo('✅ SharedPreferences 삭제 완료', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('SharedPreferences 삭제 실패', tag: _tag, error: e);
      // 계속 진행
    }
  }

  /// SQLite 데이터베이스 완전 삭제
  static Future<void> _clearDatabase({
    WidgetRef? ref,
    bool showProgress = true,
  }) async {
    try {
      if (showProgress) {
        LoggerUtils.logInfo('2️⃣ SQLite 데이터베이스 삭제 중...', tag: _tag);
      }

      if (ref != null) {
        final databaseService = ref.read(databaseServiceProvider);
        await databaseService.deleteDatabase();
      } else {
        // ref가 없는 경우 직접 데이터베이스 파일 삭제 시도
        try {
          final databasesPath = await getDatabasesPath();
          final dbPath = '$databasesPath/parabara_database.db'; // 올바른 데이터베이스 파일명
          final dbFile = File(dbPath);
          if (await dbFile.exists()) {
            await dbFile.delete();
          }
        } catch (e) {
          LoggerUtils.logWarning('데이터베이스 파일 직접 삭제 실패 (무시)', tag: _tag, error: e);
        }
      }

      if (showProgress) {
        LoggerUtils.logInfo('✅ SQLite 데이터베이스 삭제 완료', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('SQLite 데이터베이스 삭제 실패', tag: _tag, error: e);
      // 계속 진행
    }
  }

  /// 이미지 캐시 완전 삭제 (완전 삭제 시에만 파일도 삭제)
  static Future<void> _clearImageCache({bool showProgress = true}) async {
    try {
      if (showProgress) {
        LoggerUtils.logInfo('3️⃣ 이미지 캐시 삭제 중...', tag: _tag);
      }

      // 완전 삭제 시에만 실제 파일도 삭제 (회원탈퇴, 앱 초기화 등)
      // 일반적인 메모리 정리 시에는 파일 보존
      await ImageCacheManager.clearCache(); // 네트워크 캐시만
      await ImageSyncUtils.clearAllImageCache(); // 메모리 캐시만

      if (showProgress) {
        LoggerUtils.logInfo('✅ 이미지 캐시 삭제 완료', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('이미지 캐시 삭제 실패', tag: _tag, error: e);
      // 계속 진행
    }
  }

  /// 애플리케이션 문서 디렉토리의 모든 파일 삭제
  static Future<void> _clearApplicationFiles({bool showProgress = true}) async {
    try {
      if (showProgress) {
        LoggerUtils.logInfo('4️⃣ 애플리케이션 파일 삭제 중...', tag: _tag);
      }

      final appDir = await getApplicationDocumentsDirectory();
      if (await appDir.exists()) {
        // 디렉토리 내용 삭제 (디렉토리 자체는 유지)
        final entities = appDir.listSync();
        for (final entity in entities) {
          try {
            if (entity is File) {
              await entity.delete();
            } else if (entity is Directory) {
              await entity.delete(recursive: true);
            }
          } catch (e) {
            LoggerUtils.logWarning('파일/디렉토리 삭제 실패: ${entity.path}', tag: _tag, error: e);
          }
        }
      }

      if (showProgress) {
        LoggerUtils.logInfo('✅ 애플리케이션 파일 삭제 완료', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('애플리케이션 파일 삭제 실패', tag: _tag, error: e);
      // 계속 진행
    }
  }

  /// 임시 디렉토리 정리
  static Future<void> _clearTemporaryFiles({bool showProgress = true}) async {
    try {
      if (showProgress) {
        LoggerUtils.logInfo('5️⃣ 임시 파일 삭제 중...', tag: _tag);
      }

      final tempDir = await getTemporaryDirectory();
      if (await tempDir.exists()) {
        // 임시 디렉토리 내용 삭제
        final entities = tempDir.listSync();
        for (final entity in entities) {
          try {
            if (entity is File) {
              await entity.delete();
            } else if (entity is Directory) {
              await entity.delete(recursive: true);
            }
          } catch (e) {
            LoggerUtils.logWarning('임시 파일/디렉토리 삭제 실패: ${entity.path}', tag: _tag, error: e);
          }
        }
      }

      if (showProgress) {
        LoggerUtils.logInfo('✅ 임시 파일 삭제 완료', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('임시 파일 삭제 실패', tag: _tag, error: e);
      // 계속 진행
    }
  }

  /// Flutter 이미지 캐시 정리
  static Future<void> _clearFlutterImageCache({bool showProgress = true}) async {
    try {
      if (showProgress) {
        LoggerUtils.logInfo('6️⃣ Flutter 이미지 캐시 삭제 중...', tag: _tag);
      }

      final imageCache = PaintingBinding.instance.imageCache;
      imageCache.clear();
      imageCache.clearLiveImages();

      if (showProgress) {
        LoggerUtils.logInfo('✅ Flutter 이미지 캐시 삭제 완료', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('Flutter 이미지 캐시 삭제 실패', tag: _tag, error: e);
      // 계속 진행
    }
  }

  /// 로그아웃용 로컬 데이터 삭제 (온보딩 상태 유지)
  static Future<void> clearDataForLogout({WidgetRef? ref}) async {
    // 추가: 닉네임 Provider 상태도 초기화
    if (ref != null) {
      try {
        LoggerUtils.logInfo('닉네임 Provider 상태 초기화', tag: _tag);
        ref.read(nicknameProvider.notifier).clearNickname();
      } catch (e) {
        LoggerUtils.logWarning('닉네임 Provider 초기화 실패', tag: _tag, error: e);
      }
    }
    
    await clearAllLocalData(
      includeOnboarding: false,
      showProgress: true,
      ref: ref,
    );
  }

  /// 회원탈퇴용 로컬 데이터 삭제 (모든 데이터 삭제)
  static Future<void> clearDataForAccountDeletion({WidgetRef? ref}) async {
    // Provider 상태 먼저 안전하게 정리
    if (ref != null) {
      try {
        LoggerUtils.logInfo('회원탈퇴: Provider 상태 정리 시작', tag: _tag);

        // 모든 Provider 상태를 안전하게 정리
        await _clearAllProviderStates(ref);

        LoggerUtils.logInfo('회원탈퇴: Provider 상태 정리 완료', tag: _tag);
      } catch (e) {
        LoggerUtils.logWarning('Provider 상태 정리 중 오류 (계속 진행): $e', tag: _tag);
      }
    }

    await clearAllLocalData(
      includeOnboarding: true,
      showProgress: true,
      ref: ref,
    );
  }

  /// 모든 Provider 상태를 안전하게 정리
  static Future<void> _clearAllProviderStates(WidgetRef ref) async {
    LoggerUtils.logInfo('Provider 상태 정리 시작', tag: _tag);

    // 1단계: 실시간 동기화 서비스 먼저 중지 (다른 Provider들과의 연결 차단)
    try {
      LoggerUtils.logInfo('1단계: 실시간 동기화 서비스 중지 (로컬 전용 모드)', tag: _tag);
      // 로컬 전용 모드: 실시간 동기화 서비스 제거됨
      LoggerUtils.logInfo('실시간 동기화 서비스 정리 완료 (로컬 전용 모드)', tag: _tag);
    } catch (e) {
      LoggerUtils.logWarning('실시간 동기화 서비스 정리 실패: $e', tag: _tag);
    }

    // 2단계: 데이터 Provider들 정리 (의존성 순서 고려)
    try {
      LoggerUtils.logInfo('2단계: 데이터 Provider 정리', tag: _tag);

      // 판매 로그 Provider 먼저 정리 (다른 Provider들을 참조하므로)
      await _safeDisposeProvider(ref, 'salesLogProvider', () async {
        // 판매 로그 Provider는 앱 재시작으로 해결
      });

      // 상품 Provider 정리
      await _safeDisposeProvider(ref, 'productProvider', () async {
        // 상품 Provider는 앱 재시작으로 해결
      });

      // 이벤트 Provider 정리
      await _safeDisposeProvider(ref, 'eventProvider', () async {
        // 이벤트 Provider는 앱 재시작으로 해결
      });

      // 각 Provider 정리 사이에 짧은 대기
      await Future.delayed(const Duration(milliseconds: 200));

    } catch (e) {
      LoggerUtils.logWarning('데이터 Provider 정리 중 오류: $e', tag: _tag);
    }

    // 3단계: UI 관련 Provider들 정리
    try {
      LoggerUtils.logInfo('3단계: UI Provider 정리', tag: _tag);

      // 닉네임 Provider 정리
      await _safeDisposeProvider(ref, 'nicknameProvider', () async {
        ref.read(nicknameProvider.notifier).clearNickname();
      });

      // EventWorkspaceManager 정리
      await _safeDisposeProvider(ref, 'eventWorkspaceManager', () async {
        final workspaceManager = EventWorkspaceManager.instance;
        await workspaceManager.clearAllWorkspaces();

        // 추가: SharedPreferences에서 워크스페이스 관련 모든 키 확실히 삭제
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove('current_event_workspace_id');
        await prefs.remove('lastWorkspaceId');
        await prefs.remove('lastWorkspaceUpdatedAt');
        LoggerUtils.logInfo('워크스페이스 관련 SharedPreferences 키 추가 삭제 완료', tag: _tag);
      });

      // StateSyncManager 상태 캐시 정리
      await _safeDisposeProvider(ref, 'stateSyncManager', () async {
        StateSyncManager().clearAllStates();
        LoggerUtils.logInfo('StateSyncManager 상태 캐시 정리 완료', tag: _tag);
      });

      LoggerUtils.logInfo('UI Provider 정리 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logWarning('UI Provider 정리 실패: $e', tag: _tag);
    }

    // 4단계: 모든 Provider 정리 완료 후 충분한 대기
    try {
      LoggerUtils.logInfo('4단계: Provider 정리 완료 대기', tag: _tag);
      await Future.delayed(const Duration(milliseconds: 800));
      LoggerUtils.logInfo('Provider 상태 정리 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('Provider 상태 정리 완료 대기 중 오류: $e', tag: _tag);
    }
  }

  /// 개별 Provider를 안전하게 정리하는 헬퍼 메서드
  static Future<void> _safeDisposeProvider(
    WidgetRef ref,
    String providerName,
    Future<void> Function() disposeAction
  ) async {
    try {
      LoggerUtils.logInfo('$providerName 정리 시작', tag: _tag);
      await disposeAction();
      LoggerUtils.logInfo('$providerName 정리 완료', tag: _tag);
    } catch (e) {
      LoggerUtils.logWarning('$providerName 정리 실패: $e', tag: _tag);
      // 개별 Provider 정리 실패는 전체 과정을 중단하지 않음
    }
  }

  /// 특정 사용자의 데이터만 삭제 (프로필 이미지 등)
  static Future<void> clearUserSpecificData(String userId, {bool showProgress = true}) async {
    try {
      if (showProgress) {
        LoggerUtils.logInfo('🗑️ 사용자별 데이터 삭제 시작: $userId', tag: _tag);
      }

      // 프로필 이미지 파일 삭제
      final appDir = await getApplicationDocumentsDirectory();
      final entities = appDir.listSync();
      
      for (final entity in entities) {
        if (entity is File) {
          final fileName = entity.path.split('/').last;
          if (fileName.contains('profile_image_${userId}_') || 
              fileName.contains('profile_${userId}_') ||
              fileName == 'profile_image_$userId.jpg') {
            try {
              await entity.delete();
              if (showProgress) {
                LoggerUtils.logInfo('사용자 파일 삭제: $fileName', tag: _tag);
              }
            } catch (e) {
              LoggerUtils.logWarning('사용자 파일 삭제 실패: $fileName', tag: _tag, error: e);
            }
          }
        }
      }

      if (showProgress) {
        LoggerUtils.logInfo('✅ 사용자별 데이터 삭제 완료: $userId', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('사용자별 데이터 삭제 실패', tag: _tag, error: e);
    }
  }
}
