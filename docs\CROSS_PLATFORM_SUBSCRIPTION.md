# 🌟 크로스 플랫폼 구독 지원 가이드

## 📱 지원되는 시나리오

### ✅ 완전 지원
- **iPad에서 Apple ID로 구독 구매** → **Galaxy 폰에서 같은 앱 계정으로 사용** ✅
- **Android에서 Google 계정으로 구독 구매** → **iPhone에서 같은 앱 계정으로 사용** ✅
- **구독 복원**: 원본 구매자라면 어떤 플랫폼에서든 복원 가능 ✅

### 🔐 보안 정책
- **구독 소유권**: 앱 계정(Firebase UID) 기반으로 관리
- **구독 도용 방지**: 다른 사용자의 구독은 사용 불가
- **플랫폼 정보 추적**: 구매한 플랫폼 정보 저장 및 표시

## 🛠️ 기술적 구현

### 1. 구독 데이터 구조
```dart
UserSubscription {
  userId: "firebase_uid",                    // 앱 계정 ID
  originalPurchaserUserId: "firebase_uid",   // 원본 구매자
  platform: "apple" | "google",             // 구매 플랫폼
  platformAccountId: "apple_id | google_id", // 플랫폼 계정 ID
  purchaseId: "transaction_id",              // 구매 ID
  // ... 기타 필드
}
```

### 2. 구독 권한 확인 로직
```dart
bool canBeUsedBy(String currentUserId) {
  // 무료 플랜은 누구나 사용 가능
  if (planType == SubscriptionPlanType.free) return true;
  
  // 유료 플랜은 원본 구매자만 사용 가능 (플랫폼 무관)
  return isOriginalPurchaser(currentUserId);
}
```

### 3. 크로스 플랫폼 복원 지원
```dart
// 원본 구매자라면 플랫폼이 달라도 복원 허용
if (isOriginalPurchaser) {
  if (isSamePlatformAccount) {
    // 같은 플랫폼에서 복원
  } else {
    // 크로스 플랫폼 복원 (예: Apple → Android)
  }
  return true;
}
```

## 🎯 사용자 경험

### 구독 상태 표시
- **플랫폼 정보**: "Apple 구독 (iPad/iPhone에서도 사용 가능)"
- **크로스 플랫폼 안내**: 구독 복원 시 크로스 플랫폼 지원 명시
- **만료일 표시**: 구독 만료일 정보 제공

### 구독 복원 프로세스
1. 사용자가 "구독 복원" 버튼 클릭
2. 현재 플랫폼에서 구독 확인
3. 없으면 다른 플랫폼 구독 확인
4. 원본 구매자라면 크로스 플랫폼 복원 허용

## 🔒 보안 강화 사항

### 구독 도용 방지
- **계정 기반 소유권**: 앱 계정과 구독 연결
- **원본 구매자 추적**: 실제 구매한 사용자만 사용 가능
- **플랫폼 정보 저장**: 구매 플랫폼 및 계정 정보 보관

### 보안 모니터링
- **의심스러운 활동 감지**: 구독 이전 시도 등
- **보안 이벤트 로깅**: Firebase에 보안 로그 저장
- **구독 점수 시스템**: 구독 보안 점수 계산

## 📋 테스트 시나리오

### 정상 시나리오
1. **iPad Apple ID 구독** → **Galaxy 같은 계정 로그인** → **구독 사용 가능** ✅
2. **Android Google 구독** → **iPhone 같은 계정 로그인** → **구독 사용 가능** ✅
3. **크로스 플랫폼 복원** → **원본 구매자** → **복원 성공** ✅

### 보안 시나리오
1. **다른 사용자 계정** → **구독 사용 시도** → **차단** ✅
2. **구독 복원** → **다른 사용자** → **복원 실패** ✅
3. **의심스러운 활동** → **보안 로그 기록** ✅

## 🚀 향후 개선 사항

### 추가 기능
- **가족 공유 지원**: Apple/Google 가족 공유 연동
- **디바이스 제한**: 동시 사용 디바이스 수 제한
- **사용량 모니터링**: 구독 사용 패턴 분석

### UI/UX 개선
- **플랫폼 아이콘**: Apple/Google 아이콘으로 시각적 구분
- **크로스 플랫폼 가이드**: 사용자 가이드 페이지 추가
- **구독 히스토리**: 구독 변경 이력 표시

## 📞 문의 및 지원

구독 관련 문의사항이 있으시면 앱 내 고객지원을 통해 연락해주세요.
- 크로스 플랫폼 사용 문제
- 구독 복원 실패
- 결제 관련 문의
