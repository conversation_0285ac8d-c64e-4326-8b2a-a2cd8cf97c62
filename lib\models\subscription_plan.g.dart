// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subscription_plan.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_SubscriptionPlan _$SubscriptionPlanFromJson(Map<String, dynamic> json) =>
    _SubscriptionPlan(
      type: $enumDecode(_$SubscriptionPlanTypeEnumMap, json['type']),
      name: json['name'] as String,
      description: json['description'] as String,
      monthlyPrice: (json['monthlyPrice'] as num).toInt(),
      yearlyPrice: (json['yearlyPrice'] as num).toInt(),
      maxEvents: (json['maxEvents'] as num?)?.toInt(),
      maxProducts: (json['maxProducts'] as num?)?.toInt(),
      hasSetDiscountFeature: json['hasSetDiscountFeature'] as bool? ?? false,
      hasServiceFeature: json['hasServiceFeature'] as bool? ?? false,
      hasServerSyncFeature: json['hasServerSyncFeature'] as bool? ?? false,
      hasSellerManagementFeature:
          json['hasSellerManagementFeature'] as bool? ?? false,
      hasExcelExportFeature: json['hasExcelExportFeature'] as bool? ?? false,
      hasPdfExportFeature: json['hasPdfExportFeature'] as bool? ?? false,
      hasAdvancedStatsFeature:
          json['hasAdvancedStatsFeature'] as bool? ?? false,
      hasGoogleDriveBackupFeature:
          json['hasGoogleDriveBackupFeature'] as bool? ?? false,
    );

Map<String, dynamic> _$SubscriptionPlanToJson(_SubscriptionPlan instance) =>
    <String, dynamic>{
      'type': _$SubscriptionPlanTypeEnumMap[instance.type]!,
      'name': instance.name,
      'description': instance.description,
      'monthlyPrice': instance.monthlyPrice,
      'yearlyPrice': instance.yearlyPrice,
      'maxEvents': instance.maxEvents,
      'maxProducts': instance.maxProducts,
      'hasSetDiscountFeature': instance.hasSetDiscountFeature,
      'hasServiceFeature': instance.hasServiceFeature,
      'hasServerSyncFeature': instance.hasServerSyncFeature,
      'hasSellerManagementFeature': instance.hasSellerManagementFeature,
      'hasExcelExportFeature': instance.hasExcelExportFeature,
      'hasPdfExportFeature': instance.hasPdfExportFeature,
      'hasAdvancedStatsFeature': instance.hasAdvancedStatsFeature,
      'hasGoogleDriveBackupFeature': instance.hasGoogleDriveBackupFeature,
    };

const _$SubscriptionPlanTypeEnumMap = {
  SubscriptionPlanType.free: 'free',
  SubscriptionPlanType.plus: 'plus',
};

_UserSubscription _$UserSubscriptionFromJson(Map<String, dynamic> json) =>
    _UserSubscription(
      userId: json['userId'] as String,
      planType: $enumDecode(_$SubscriptionPlanTypeEnumMap, json['planType']),
      subscriptionStartDate: json['subscriptionStartDate'] == null
          ? null
          : DateTime.parse(json['subscriptionStartDate'] as String),
      subscriptionEndDate: json['subscriptionEndDate'] == null
          ? null
          : DateTime.parse(json['subscriptionEndDate'] as String),
      isActive: json['isActive'] as bool? ?? true,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      originalPurchaserUserId: json['originalPurchaserUserId'] as String?,
      platformAccountId: json['platformAccountId'] as String?,
      platform: json['platform'] as String?,
      purchaseId: json['purchaseId'] as String?,
      productId: json['productId'] as String?,
      source: json['source'] as String? ?? 'in_app_purchase',
      adminBonusDays: (json['adminBonusDays'] as num?)?.toInt(),
      adminBonusEndDate: json['adminBonusEndDate'] == null
          ? null
          : DateTime.parse(json['adminBonusEndDate'] as String),
      isTransferred: json['isTransferred'] as bool? ?? false,
      transferHistory: json['transferHistory'] as String?,
    );

Map<String, dynamic> _$UserSubscriptionToJson(
  _UserSubscription instance,
) => <String, dynamic>{
  'userId': instance.userId,
  'planType': _$SubscriptionPlanTypeEnumMap[instance.planType]!,
  'subscriptionStartDate': instance.subscriptionStartDate?.toIso8601String(),
  'subscriptionEndDate': instance.subscriptionEndDate?.toIso8601String(),
  'isActive': instance.isActive,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
  'originalPurchaserUserId': instance.originalPurchaserUserId,
  'platformAccountId': instance.platformAccountId,
  'platform': instance.platform,
  'purchaseId': instance.purchaseId,
  'productId': instance.productId,
  'source': instance.source,
  'adminBonusDays': instance.adminBonusDays,
  'adminBonusEndDate': instance.adminBonusEndDate?.toIso8601String(),
  'isTransferred': instance.isTransferred,
  'transferHistory': instance.transferHistory,
};
