import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'set_discount.freezed.dart';
part 'set_discount.g.dart';

/// 세트 할인 조건 타입
enum SetDiscountConditionType {
  /// 특정 상품 조합 (기존 방식)
  productCombination,
  /// 최소 구매 금액
  minimumAmount,
  /// 카테고리별 최소 수량
  categoryQuantity,
  /// 특정 상품군에서 최소 수량
  productGroupQuantity,
}

/// 최소 구매 금액 기준 타입
enum MinimumAmountBasisType {
  /// 다른 할인 적용 전 금액 기준
  beforeOtherDiscounts,
  /// 다른 할인 적용 후 금액 기준
  afterOtherDiscounts,
}

/// 카테고리별 수량 조건
class CategoryQuantityCondition {
  final int categoryId;
  final int minimumQuantity;

  const CategoryQuantityCondition({
    required this.categoryId,
    required this.minimumQuantity,
  });

  factory CategoryQuantityCondition.fromJson(Map<String, dynamic> json) {
    return CategoryQuantityCondition(
      categoryId: json['categoryId'] as int,
      minimumQuantity: json['minimumQuantity'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'categoryId': categoryId,
      'minimumQuantity': minimumQuantity,
    };
  }
}

/// 상품군 수량 조건
class ProductGroupQuantityCondition {
  final List<int> productIds;
  final int minimumQuantity;

  const ProductGroupQuantityCondition({
    required this.productIds,
    required this.minimumQuantity,
  });

  factory ProductGroupQuantityCondition.fromJson(Map<String, dynamic> json) {
    return ProductGroupQuantityCondition(
      productIds: (json['productIds'] as List<dynamic>).cast<int>(),
      minimumQuantity: json['minimumQuantity'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'productIds': productIds,
      'minimumQuantity': minimumQuantity,
    };
  }
}

/// 세트 할인 정보를 표현하는 데이터 모델 클래스입니다.
/// - 세트명, 할인 금액, 다양한 할인 조건 등 포함
/// - DB 연동, CRUD, 필터/정렬/검색 등에서 사용
/// - freezed를 사용하여 불변 객체로 생성
@freezed
abstract class SetDiscount with _$SetDiscount {
  const factory SetDiscount({
    int? id,
    required String name,
    required int discountAmount,

    // 할인 조건 타입
    @Default(SetDiscountConditionType.productCombination) SetDiscountConditionType conditionType,

    // 기존 상품 조합 조건 (conditionType이 productCombination일 때 사용)
    @Default([]) List<int> productIds,

    // 최소 구매 금액 조건 (conditionType이 minimumAmount일 때 사용)
    @Default(0) int minimumAmount,

    // 최소 구매 금액 기준 타입 (conditionType이 minimumAmount일 때 사용)
    @Default(MinimumAmountBasisType.beforeOtherDiscounts) MinimumAmountBasisType minimumAmountBasisType,

    // 카테고리별 수량 조건 (conditionType이 categoryQuantity일 때 사용)
    CategoryQuantityCondition? categoryCondition,

    // 상품군 수량 조건 (conditionType이 productGroupQuantity일 때 사용)
    ProductGroupQuantityCondition? productGroupCondition,

    // 반복 적용 여부 (카테고리별/상품군별 할인에서 조건을 만족할 때마다 반복 적용할지 여부)
    @Default(false) bool allowMultipleApplications,

    @Default(true) bool isActive,
    required DateTime createdAt,
    DateTime? updatedAt,
    @Default(1) int eventId, // 행사 ID 추가
  }) = _SetDiscount;

  factory SetDiscount.fromJson(Map<String, dynamic> json) => _$SetDiscountFromJson(json);

  // SQLite 맵에서 직접 생성
  factory SetDiscount.fromMap(Map<String, dynamic> map) {
    // productIds는 JSON 문자열로 저장되므로 파싱 필요
    List<int> productIds = [];
    if (map['productIds'] != null) {
      try {
        final productIdsJson = map['productIds'] as String;
        final List<dynamic> productIdsList =
            (jsonDecode(productIdsJson) as List<dynamic>);
        productIds = productIdsList.map((e) => e as int).toList();
      } catch (e) {
        // 파싱 실패 시 빈 리스트
        productIds = [];
      }
    }

    // 할인 조건 타입 파싱
    SetDiscountConditionType conditionType = SetDiscountConditionType.productCombination;
    if (map['conditionType'] != null) {
      try {
        conditionType = SetDiscountConditionType.values.firstWhere(
          (e) => e.toString() == map['conditionType'],
          orElse: () => SetDiscountConditionType.productCombination,
        );
      } catch (e) {
        conditionType = SetDiscountConditionType.productCombination;
      }
    }

    // 카테고리 조건 파싱
    CategoryQuantityCondition? categoryCondition;
    if (map['categoryCondition'] != null) {
      try {
        final categoryJson = jsonDecode(map['categoryCondition'] as String);
        categoryCondition = CategoryQuantityCondition.fromJson(categoryJson);
      } catch (e) {
        categoryCondition = null;
      }
    }

    // 상품군 조건 파싱
    ProductGroupQuantityCondition? productGroupCondition;
    if (map['productGroupCondition'] != null) {
      try {
        final productGroupJson = jsonDecode(map['productGroupCondition'] as String);
        productGroupCondition = ProductGroupQuantityCondition.fromJson(productGroupJson);
      } catch (e) {
        productGroupCondition = null;
      }
    }

    // minimumAmountBasisType 파싱
    MinimumAmountBasisType minimumAmountBasisType = MinimumAmountBasisType.beforeOtherDiscounts;
    if (map['minimumAmountBasisType'] != null) {
      final basisTypeString = map['minimumAmountBasisType'] as String;
      minimumAmountBasisType = MinimumAmountBasisType.values.firstWhere(
        (type) => type.toString() == basisTypeString,
        orElse: () => MinimumAmountBasisType.beforeOtherDiscounts,
      );
    }

    return SetDiscount(
      id: map['id'],
      name: map['name'] ?? '',
      discountAmount: map['discountAmount'] ?? 0,
      conditionType: conditionType,
      productIds: productIds,
      minimumAmount: map['minimumAmount'] ?? 0,
      minimumAmountBasisType: minimumAmountBasisType,
      categoryCondition: categoryCondition,
      productGroupCondition: productGroupCondition,
      allowMultipleApplications: map['allowMultipleApplications'] == 1 || map['allowMultipleApplications'] == true,
      isActive: map['isActive'] == 1 || map['isActive'] == true,
      createdAt: map['createdAt'] != null
          ? DateTime.parse(map['createdAt'])
          : DateTime.now(),
      updatedAt: map['updatedAt'] != null
          ? DateTime.parse(map['updatedAt'])
          : null,
      eventId: map['eventId'] as int? ?? 1, // 기본값 1
    );
  }

  // 현재 타임스탬프로 생성하는 팩토리
  factory SetDiscount.create({
    int? id,
    required String name,
    required int discountAmount,
    SetDiscountConditionType conditionType = SetDiscountConditionType.productCombination,
    List<int> productIds = const [],
    int minimumAmount = 0,
    MinimumAmountBasisType minimumAmountBasisType = MinimumAmountBasisType.beforeOtherDiscounts,
    CategoryQuantityCondition? categoryCondition,
    ProductGroupQuantityCondition? productGroupCondition,
    bool allowMultipleApplications = false,
    bool isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
    required int eventId, // 행사 ID 필수로 변경
  }) {
    return SetDiscount(
      id: id,
      name: name,
      discountAmount: discountAmount,
      conditionType: conditionType,
      productIds: productIds,
      minimumAmount: minimumAmount,
      minimumAmountBasisType: minimumAmountBasisType,
      categoryCondition: categoryCondition,
      productGroupCondition: productGroupCondition,
      allowMultipleApplications: allowMultipleApplications,
      isActive: isActive,
      createdAt: createdAt ?? DateTime.now(),
      updatedAt: updatedAt,
      eventId: eventId,
    );
  }
}

// SQLite 맵 변환을 위한 Extension
extension SetDiscountMapper on SetDiscount {
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'discountAmount': discountAmount,
      'conditionType': conditionType.toString(),
      'productIds': jsonEncode(productIds), // JSON 문자열로 저장
      'minimumAmount': minimumAmount,
      'minimumAmountBasisType': this.minimumAmountBasisType.toString(),
      'categoryCondition': categoryCondition != null
          ? jsonEncode(categoryCondition!.toJson())
          : null,
      'productGroupCondition': productGroupCondition != null
          ? jsonEncode(productGroupCondition!.toJson())
          : null,
      'allowMultipleApplications': this.allowMultipleApplications ? 1 : 0,
      'isActive': isActive ? 1 : 0,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'eventId': eventId,
    };
  }
}
