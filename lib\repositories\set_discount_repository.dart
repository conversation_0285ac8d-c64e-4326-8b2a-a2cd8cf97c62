import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import '../models/set_discount.dart';
import '../services/database_service.dart';
import '../utils/logger_utils.dart';

/// 세트 할인 데이터 접근을 담당하는 Repository 클래스입니다.
/// - CRUD 작업, 필터링, 정렬 등의 데이터베이스 작업 수행
/// - 행사별 데이터 분리 관리
class SetDiscountRepository {
  final DatabaseService database;

  SetDiscountRepository({required this.database});

  /// 모든 세트 할인 조회 (특정 행사)
  Future<List<SetDiscount>> getAllSetDiscounts({int? eventId}) async {
    try {
      final db = await database.database;
      final List<Map<String, dynamic>> maps;
      
      if (eventId != null) {
        maps = await db.query(
          DatabaseServiceImpl.setDiscountsTable,
          where: 'eventId = ?',
          whereArgs: [eventId],
          orderBy: 'createdAt DESC',
        );
      } else {
        maps = await db.query(
          DatabaseServiceImpl.setDiscountsTable,
          orderBy: 'createdAt DESC',
        );
      }

      return maps.map((map) => SetDiscount.fromMap(map)).toList();
    } catch (e) {
      LoggerUtils.logError('Failed to get all set discounts', error: e, tag: 'SetDiscountRepository');
      return [];
    }
  }

  /// 활성화된 세트 할인만 조회 (특정 행사)
  Future<List<SetDiscount>> getActiveSetDiscounts({int? eventId}) async {
    try {
      final db = await database.database;
      final List<Map<String, dynamic>> maps;
      
      if (eventId != null) {
        maps = await db.query(
          DatabaseServiceImpl.setDiscountsTable,
          where: 'eventId = ? AND isActive = 1',
          whereArgs: [eventId],
          orderBy: 'createdAt DESC',
        );
      } else {
        maps = await db.query(
          DatabaseServiceImpl.setDiscountsTable,
          where: 'isActive = 1',
          orderBy: 'createdAt DESC',
        );
      }

      return maps.map((map) => SetDiscount.fromMap(map)).toList();
    } catch (e) {
      LoggerUtils.logError('Failed to get active set discounts', error: e, tag: 'SetDiscountRepository');
      return [];
    }
  }

  /// ID로 세트 할인 조회
  Future<SetDiscount?> getSetDiscountById(int id) async {
    try {
      final db = await database.database;
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseServiceImpl.setDiscountsTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isNotEmpty) {
        return SetDiscount.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      LoggerUtils.logError('Failed to get set discount by id: $id', error: e, tag: 'SetDiscountRepository');
      return null;
    }
  }

  /// 세트 할인 추가
  Future<int?> insertSetDiscount(SetDiscount setDiscount) async {
    try {
      final db = await database.database;

      // 기본 유효성 검사 (조건 타입별로 적절한 검사)
      if (setDiscount.name.isEmpty || setDiscount.discountAmount <= 0) {
        return null;
      }

      // 조건 타입별 추가 유효성 검사
      switch (setDiscount.conditionType) {
        case SetDiscountConditionType.productCombination:
          if (setDiscount.productIds.isEmpty) {
            LoggerUtils.logWarning('Product combination discount requires product IDs', tag: 'SetDiscountRepository');
            return null;
          }
          break;
        case SetDiscountConditionType.minimumAmount:
          if (setDiscount.minimumAmount <= 0) {
            LoggerUtils.logWarning('Minimum amount discount requires valid minimum amount', tag: 'SetDiscountRepository');
            return null;
          }
          break;
        case SetDiscountConditionType.categoryQuantity:
          if (setDiscount.categoryCondition == null) {
            LoggerUtils.logWarning('Category quantity discount requires category condition', tag: 'SetDiscountRepository');
            return null;
          }
          break;
        case SetDiscountConditionType.productGroupQuantity:
          if (setDiscount.productGroupCondition == null) {
            LoggerUtils.logWarning('Product group quantity discount requires product group condition', tag: 'SetDiscountRepository');
            return null;
          }
          break;
      }

      final id = await db.insert(
        DatabaseServiceImpl.setDiscountsTable,
        setDiscount.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      LoggerUtils.logInfo('Set discount inserted with id: $id', tag: 'SetDiscountRepository');
      return id;
    } catch (e) {
      LoggerUtils.logError('Failed to insert set discount: ${setDiscount.name}', error: e, tag: 'SetDiscountRepository');
      return null;
    }
  }

  /// 세트 할인 수정
  Future<bool> updateSetDiscount(SetDiscount setDiscount) async {
    try {
      final db = await database.database;
      final updatedSetDiscount = setDiscount.copyWith(
        updatedAt: DateTime.now(),
      );
      
      final count = await db.update(
        DatabaseServiceImpl.setDiscountsTable,
        updatedSetDiscount.toMap(),
        where: 'id = ?',
        whereArgs: [setDiscount.id],
      );
      
      LoggerUtils.logInfo('Set discount updated: ${setDiscount.id}', tag: 'SetDiscountRepository');
      return count > 0;
    } catch (e) {
      LoggerUtils.logError('Failed to update set discount: ${setDiscount.id}', error: e, tag: 'SetDiscountRepository');
      return false;
    }
  }

  /// 세트 할인 삭제
  Future<bool> deleteSetDiscount(int id) async {
    try {
      final db = await database.database;
      final count = await db.delete(
        DatabaseServiceImpl.setDiscountsTable,
        where: 'id = ?',
        whereArgs: [id],
      );
      
      LoggerUtils.logInfo('Set discount deleted: $id', tag: 'SetDiscountRepository');
      return count > 0;
    } catch (e) {
      LoggerUtils.logError('Failed to delete set discount: $id', error: e, tag: 'SetDiscountRepository');
      return false;
    }
  }

  /// 세트 할인 활성화/비활성화
  Future<bool> toggleSetDiscountActive(int id, bool isActive) async {
    try {
      final db = await database.database;
      final count = await db.update(
        DatabaseServiceImpl.setDiscountsTable,
        {
          'isActive': isActive ? 1 : 0,
          'updatedAt': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [id],
      );
      
      LoggerUtils.logInfo('Set discount active status changed: $id -> $isActive', tag: 'SetDiscountRepository');
      return count > 0;
    } catch (e) {
      LoggerUtils.logError('Failed to toggle set discount active: $id', error: e, tag: 'SetDiscountRepository');
      return false;
    }
  }

  /// 특정 상품이 포함된 세트 할인 조회 (모든 조건 타입 고려)
  Future<List<SetDiscount>> getSetDiscountsByProductId(int productId, {int? eventId}) async {
    try {
      LoggerUtils.logInfo('getSetDiscountsByProductId 시작 - productId: $productId, eventId: $eventId', tag: 'SetDiscountRepository');

      final db = await database.database;
      final List<Map<String, dynamic>> maps;

      if (eventId != null) {
        maps = await db.query(
          DatabaseServiceImpl.setDiscountsTable,
          where: 'eventId = ? AND isActive = 1',
          whereArgs: [eventId],
        );
      } else {
        maps = await db.query(
          DatabaseServiceImpl.setDiscountsTable,
          where: 'isActive = 1',
        );
      }

      LoggerUtils.logInfo('DB에서 조회된 활성 세트 할인 개수: ${maps.length}', tag: 'SetDiscountRepository');

      final setDiscounts = maps.map((map) => SetDiscount.fromMap(map)).toList();
      final relatedSetDiscounts = <SetDiscount>[];

      for (final setDiscount in setDiscounts) {
        LoggerUtils.logInfo('세트 할인 검사: ${setDiscount.name} (ID: ${setDiscount.id}, 타입: ${setDiscount.conditionType})', tag: 'SetDiscountRepository');

        bool isRelated = false;

        switch (setDiscount.conditionType) {
          case SetDiscountConditionType.productCombination:
            // 상품 조합 할인: productIds에 해당 상품이 포함된 경우
            LoggerUtils.logInfo('상품 조합 할인 - productIds: ${setDiscount.productIds}, 찾는 productId: $productId', tag: 'SetDiscountRepository');
            if (setDiscount.productIds.contains(productId)) {
              isRelated = true;
              LoggerUtils.logInfo('상품 조합 할인에서 일치하는 상품 발견!', tag: 'SetDiscountRepository');
            }
            break;

          case SetDiscountConditionType.productGroupQuantity:
            // 상품군 수량 할인: productGroupCondition의 productIds에 해당 상품이 포함된 경우
            final groupProductIds = setDiscount.productGroupCondition?.productIds ?? [];
            LoggerUtils.logInfo('상품군 수량 할인 - productIds: $groupProductIds, 찾는 productId: $productId', tag: 'SetDiscountRepository');
            if (groupProductIds.contains(productId)) {
              isRelated = true;
              LoggerUtils.logInfo('상품군 수량 할인에서 일치하는 상품 발견!', tag: 'SetDiscountRepository');
            }
            break;

          case SetDiscountConditionType.minimumAmount:
          case SetDiscountConditionType.categoryQuantity:
            // 최소 구매 금액 할인과 카테고리별 수량 할인은 특정 상품에 의존하지 않으므로 삭제하지 않음
            LoggerUtils.logInfo('최소 구매 금액/카테고리별 수량 할인은 상품 삭제와 무관하므로 건너뜀', tag: 'SetDiscountRepository');
            break;
        }

        if (isRelated) {
          relatedSetDiscounts.add(setDiscount);
          LoggerUtils.logInfo('관련 세트 할인으로 추가됨: ${setDiscount.name}', tag: 'SetDiscountRepository');
        }
      }

      LoggerUtils.logInfo('상품 ID $productId와 관련된 세트 할인 ${relatedSetDiscounts.length}개 반환', tag: 'SetDiscountRepository');
      return relatedSetDiscounts;
    } catch (e) {
      LoggerUtils.logError('Failed to get set discounts by product id: $productId', error: e, tag: 'SetDiscountRepository');
      return [];
    }
  }

  /// 특정 카테고리의 세트 할인 조회
  Future<List<SetDiscount>> getSetDiscountsByCategoryId(int categoryId, {int? eventId}) async {
    try {
      final db = await database.database;
      final List<Map<String, dynamic>> maps;

      if (eventId != null) {
        maps = await db.query(
          DatabaseServiceImpl.setDiscountsTable,
          where: 'eventId = ? AND isActive = 1',
          whereArgs: [eventId],
        );
      } else {
        maps = await db.query(
          DatabaseServiceImpl.setDiscountsTable,
          where: 'isActive = 1',
        );
      }

      // categoryCondition에서 해당 카테고리 ID가 포함된 세트 할인만 필터링
      final setDiscounts = maps.map((map) => SetDiscount.fromMap(map)).toList();
      return setDiscounts.where((setDiscount) =>
        setDiscount.categoryCondition?.categoryId == categoryId
      ).toList();
    } catch (e) {
      LoggerUtils.logError('Failed to get set discounts by category id: $categoryId', error: e, tag: 'SetDiscountRepository');
      return [];
    }
  }

  /// 세트 할인 이름 중복 확인
  Future<bool> isNameExists(String name, {int? eventId, int? excludeId}) async {
    try {
      final db = await database.database;
      String whereClause = 'name = ?';
      List<dynamic> whereArgs = [name];
      
      if (eventId != null) {
        whereClause += ' AND eventId = ?';
        whereArgs.add(eventId);
      }
      
      if (excludeId != null) {
        whereClause += ' AND id != ?';
        whereArgs.add(excludeId);
      }
      
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseServiceImpl.setDiscountsTable,
        where: whereClause,
        whereArgs: whereArgs,
      );

      return maps.isNotEmpty;
    } catch (e) {
      LoggerUtils.logError('Failed to check name exists: $name', error: e, tag: 'SetDiscountRepository');
      return false;
    }
  }

  /// 모든 세트 할인 삭제 (복원용)
  Future<void> deleteAllSetDiscounts() async {
    try {
      final db = await database.database;

      LoggerUtils.logInfo('모든 세트 할인 삭제 시작', tag: 'SetDiscountRepository');

      final deletedCount = await db.delete(DatabaseServiceImpl.setDiscountsTable);

      LoggerUtils.logInfo('모든 세트 할인 삭제 완료: ${deletedCount}개', tag: 'SetDiscountRepository');
    } catch (e) {
      LoggerUtils.logError('모든 세트 할인 삭제 실패', error: e, tag: 'SetDiscountRepository');
      rethrow;
    }
  }
}
