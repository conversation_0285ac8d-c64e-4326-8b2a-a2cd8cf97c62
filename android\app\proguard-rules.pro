# 🔒 최고 보안 수준 ProGuard 설정

# 1. 모든 로그 제거 (정보 유출 방지)
-assumenosideeffects class android.util.Log {
    public static *** d(...);
    public static *** v(...);
    public static *** i(...);
    public static *** w(...);
    public static *** e(...);
}

# 2. System.out 제거
-assumenosideeffects class java.io.PrintStream {
    public void println(...);
    public void print(...);
}

# 3. 개발용 코드 제거
-assumenosideeffects class kotlin.jvm.internal.Intrinsics {
    static void checkParameterIsNotNull(java.lang.Object, java.lang.String);
}

# 4. Firebase 및 Google API 보안 설정
-keep class com.google.firebase.storage.** { *; }
-dontwarn com.google.firebase.storage.**

# Google Sign-In 및 Drive API 보호
-keep class com.google.android.gms.** { *; }
-dontwarn com.google.android.gms.**
-keep class com.google.api.** { *; }
-dontwarn com.google.api.**
-keep class com.google.auth.** { *; }
-dontwarn com.google.auth.**

# Google Drive API 관련 클래스 보호
-keep class com.google.api.services.drive.** { *; }
-keep class com.google.api.client.** { *; }
-keep class com.google.api.client.googleapis.** { *; }
-keep class com.google.api.client.http.** { *; }
-keep class com.google.api.client.json.** { *; }

# OAuth 및 인증 관련 클래스 보호
-keep class com.google.android.gms.auth.** { *; }
-keep class com.google.android.gms.common.** { *; }
-keep class com.google.android.gms.signin.** { *; }

# 5. 문자열 난독화 (API 키 등 보호)
-assumenosideeffects class java.lang.String {
    public static java.lang.String valueOf(***);
}

# 6. 최고 수준 난독화
-optimizations !code/simplification/arithmetic,!field/*,!class/merging/*
-optimizationpasses 7
-allowaccessmodification
-repackageclasses ''
-keepattributes *Annotation*

# 7. 스택 트레이스 정보 제거 (보안 강화)
-renamesourcefileattribute SourceFile
-keepattributes SourceFile,LineNumberTable
