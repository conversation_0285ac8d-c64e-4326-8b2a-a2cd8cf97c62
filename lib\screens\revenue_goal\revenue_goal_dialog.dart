import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import '../../models/revenue_goal.dart';
import '../../models/seller.dart';
import '../../models/event.dart';
import '../../providers/revenue_goal_provider.dart';
import '../../providers/seller_provider.dart';
import '../../providers/unified_workspace_provider.dart';
import '../../services/event_workspace_manager.dart';

import '../../utils/app_colors.dart';

import '../../utils/dialog_theme.dart' as custom_dialog;
import '../../utils/logger_utils.dart';

import '../../providers/subscription_provider.dart';
import '../../models/subscription_plan.dart';
import '../../providers/event_provider.dart';

/// 목표 수익 관리 다이얼로그 (전체/판매자별 모드 분리)
class RevenueGoalDialog extends ConsumerStatefulWidget {
  const RevenueGoalDialog({super.key});

  @override
  ConsumerState<RevenueGoalDialog> createState() => _RevenueGoalDialogState();
}

class _RevenueGoalDialogState extends ConsumerState<RevenueGoalDialog> {
  final Map<String, bool> _expandedSellers = {};
  final Map<String, Map<String, TextEditingController>> _controllers = {};
  final Map<String, Map<String, String>> _originalValues = {}; // 원본 값 저장용

  // 임시 상태 관리
  RevenueGoalMode? _tempRevenueGoalMode; // 임시 목표 수익 모드

  @override
  void initState() {
    super.initState();
    // 초기 임시 상태를 현재 워크스페이스 상태로 설정
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeTempState();
    });
  }

  /// 임시 상태 초기화
  void _initializeTempState() {
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace != null && mounted) {
      setState(() {
        _tempRevenueGoalMode = currentWorkspace.revenueGoalMode;
      });
    }
  }

  /// 목표 수익 모드 변경 (확인 다이얼로그 포함)
  Future<void> _changeRevenueGoalMode(RevenueGoalMode newMode) async {
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) return;

    // 현재 모드와 같으면 변경하지 않음
    if (_getCurrentRevenueGoalMode() == newMode) return;

    // 확인 다이얼로그 표시
    final modeText = newMode == RevenueGoalMode.overall ? '전체 목표' : '판매자별';
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('목표 수익 모드 변경'),
        content: Text('$modeText 모드로 변경하시겠습니까?\n\n이 변경사항은 대시보드에 즉시 반영됩니다.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('취소'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('확인'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      // 1. 즉시 임시 상태를 새로운 모드로 설정 (UI 즉시 반영)
      if (mounted) {
        setState(() {
          _tempRevenueGoalMode = newMode;
        });
      }

      LoggerUtils.logInfo('목표 수익 모드 변경 시작: ${currentWorkspace.revenueGoalMode} -> $newMode', tag: 'RevenueGoalDialog');

      // 실제 워크스페이스 업데이트 및 대시보드 갱신
      try {
        final updatedWorkspace = currentWorkspace.copyWith(revenueGoalMode: newMode);
        final updatedEvent = updatedWorkspace.toEvent();

        // 2. Event 테이블 업데이트
        LoggerUtils.logDebug('Event 테이블 업데이트 시작', tag: 'RevenueGoalDialog');
        await ref.read(eventNotifierProvider.notifier).updateEvent(updatedEvent);
        LoggerUtils.logDebug('Event 테이블 업데이트 완료', tag: 'RevenueGoalDialog');

        // 3. 워크스페이스 매니저에서 현재 워크스페이스를 최신 데이터로 갱신 (핵심!)
        LoggerUtils.logDebug('워크스페이스 갱신 시작', tag: 'RevenueGoalDialog');
        await EventWorkspaceManager.instance.refreshWorkspaces();
        LoggerUtils.logDebug('워크스페이스 갱신 완료', tag: 'RevenueGoalDialog');

        // 4. 관련 Provider들 강제 갱신
        LoggerUtils.logDebug('Provider 무효화 시작', tag: 'RevenueGoalDialog');
        ref.invalidate(revenueGoalStatsProvider);
        ref.invalidate(revenueGoalNotifierProvider);
        ref.invalidate(currentWorkspaceProvider);
        LoggerUtils.logDebug('Provider 무효화 완료', tag: 'RevenueGoalDialog');

        // 5. 데이터 다시 로드
        LoggerUtils.logDebug('목표 수익 데이터 로드 시작', tag: 'RevenueGoalDialog');
        await ref.read(revenueGoalNotifierProvider.notifier).loadGoals(showLoading: false);
        LoggerUtils.logDebug('목표 수익 데이터 로드 완료', tag: 'RevenueGoalDialog');

        // 6. 워크스페이스 상태 검증
        final verificationWorkspace = ref.read(currentWorkspaceProvider);
        if (verificationWorkspace?.revenueGoalMode != newMode) {
          LoggerUtils.logWarning('워크스페이스 상태 검증 실패: 예상=$newMode, 실제=${verificationWorkspace?.revenueGoalMode}', tag: 'RevenueGoalDialog');
          throw Exception('워크스페이스 상태 업데이트가 완료되지 않았습니다.');
        }

        // 7. 충분한 시간 후 임시 상태 클리어 (워크스페이스 상태에 의존하도록)
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            setState(() {
              _tempRevenueGoalMode = null; // 임시 상태 클리어
            });
            LoggerUtils.logDebug('임시 상태 클리어 완료', tag: 'RevenueGoalDialog');
          }
        });

        LoggerUtils.logInfo('목표 수익 모드 변경 완료: $newMode', tag: 'RevenueGoalDialog');
      } catch (e, stackTrace) {
        LoggerUtils.logError('목표 수익 모드 변경 실패', tag: 'RevenueGoalDialog', error: e, stackTrace: stackTrace);

        // 실패 시 임시 상태 되돌리기
        if (mounted) {
          setState(() {
            _tempRevenueGoalMode = currentWorkspace.revenueGoalMode;
          });
        }

        // 사용자에게 에러 알림
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('목표 수익 모드 변경에 실패했습니다: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  /// 현재 사용할 목표 수익 모드 반환 (임시 상태 우선)
  RevenueGoalMode _getCurrentRevenueGoalMode() {
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    final workspaceMode = currentWorkspace?.revenueGoalMode ?? RevenueGoalMode.overall;

    // 임시 상태가 있으면 우선 사용
    if (_tempRevenueGoalMode != null) {
      LoggerUtils.logDebug('임시 상태 사용: $_tempRevenueGoalMode (워크스페이스: $workspaceMode)', tag: 'RevenueGoalDialog');
      return _tempRevenueGoalMode!;
    }

    LoggerUtils.logDebug('워크스페이스 상태 사용: $workspaceMode', tag: 'RevenueGoalDialog');
    return workspaceMode;
  }



  @override
  void dispose() {
    // 모든 컨트롤러 정리
    for (final sellerControllers in _controllers.values) {
      for (final controller in sellerControllers.values) {
        controller.dispose();
      }
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final revenueGoalState = ref.watch(revenueGoalNotifierProvider);
    final sellers = ref.watch(sellerNotifierProvider).sellers;
    final currentWorkspace = ref.watch(currentWorkspaceProvider);

    if (currentWorkspace == null) {
      return AlertDialog(
        title: const Text('오류'),
        content: const Text('현재 선택된 행사가 없습니다.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('확인'),
          ),
        ],
      );
    }



    return PopScope(
      canPop: true, // 바로 나가기 허용
      child: custom_dialog.DialogTheme.buildResponsiveLargeDialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.95,
          constraints: BoxConstraints(
            maxHeight: custom_dialog.DialogTheme.getResponsiveMaxHeight(context),
          ),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(16), // 상하 일관된 라운딩
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 헤더 (체크리스트 편집 다이얼로그 스타일)
              Padding(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.arrow_back, color: AppColors.onSurfaceVariant),
                      onPressed: _onBackPressed,
                      tooltip: '뒤로가기',
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '목표 수익 관리',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        color: AppColors.onSurface,
                      ),
                    ),
                    const Spacer(),
                  ],
                ),
              ),

              // 모드 토글 버튼
              Container(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '목표 관리 방식',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.primaryTextColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: Consumer(
                            builder: (context, ref, child) {
                              final subscriptionAsync = ref.watch(subscriptionNotifierProvider);
                              final planType = subscriptionAsync.maybeWhen(
                                data: (plan) => plan,
                                orElse: () => SubscriptionPlanType.free,
                              );

                              // 플러스플랜에서만 판매자별 옵션 표시
                              final segments = planType == SubscriptionPlanType.plus // 플러스 플랜에서 판매자별 관리 가능
                                  ? const [
                                      ButtonSegment<RevenueGoalMode>(
                                        value: RevenueGoalMode.overall,
                                        label: Text('전체 목표'),
                                        icon: Icon(Icons.groups),
                                      ),
                                      ButtonSegment<RevenueGoalMode>(
                                        value: RevenueGoalMode.seller,
                                        label: Text('판매자별'),
                                        icon: Icon(Icons.person),
                                      ),
                                    ]
                                  : const [
                                      ButtonSegment<RevenueGoalMode>(
                                        value: RevenueGoalMode.overall,
                                        label: Text('전체 목표'),
                                        icon: Icon(Icons.groups),
                                      ),
                                    ];

                              return SegmentedButton<RevenueGoalMode>(
                                segments: segments,
                                selected: {_getCurrentRevenueGoalMode()},
                                onSelectionChanged: (Set<RevenueGoalMode> newSelection) {
                                  _changeRevenueGoalMode(newSelection.first);
                                },
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _getCurrentRevenueGoalMode() == RevenueGoalMode.overall
                          ? '행사 전체의 목표 수익을 날짜별로 설정합니다.'
                          : '각 판매자별로 목표 수익을 날짜별로 설정합니다.',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.secondaryTextColor,
                      ),
                    ),
                  ],
                ),
              ),

              // 컨텐츠 (스크롤 가능)
              Expanded(
                child: revenueGoalState.isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : SingleChildScrollView(
                        child: _buildGoalsList(sellers, revenueGoalState.goals),
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 목표 목록 UI (모드에 따라 다르게 표시)
  Widget _buildGoalsList(List<Seller> sellers, List<RevenueGoal> goals) {
    final currentWorkspace = ref.read(currentWorkspaceProvider)!;
    final eventDates = _getEventDateRange(currentWorkspace);

    // 디버그 로그 추가
    LoggerUtils.logDebug('목표 목록 빌드: 판매자 ${sellers.length}개, 목표 ${goals.length}개, 모드: ${_getCurrentRevenueGoalMode()}', tag: 'RevenueGoalDialog');

    if (_getCurrentRevenueGoalMode() == RevenueGoalMode.overall) {
      // 전체 모드: 날짜별 전체 목표만 표시
      return _buildOverallGoalsList(eventDates, goals);
    } else {
      // 판매자별 모드: 판매자별로 접을 수 있는 섹션
      return _buildSellerGoalsList(sellers, eventDates, goals);
    }
  }

  /// 전체 목표 목록 UI
  Widget _buildOverallGoalsList(List<DateTime> eventDates, List<RevenueGoal> goals) {
    final overallGoals = goals.where((g) => g.sellerId == null).toList();
    final totalTarget = overallGoals.fold<double>(0, (sum, goal) => sum + goal.targetAmount);

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 총 목표 수익 표시
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.primarySeed.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: LayoutBuilder(
              builder: (context, constraints) {
                final isNarrow = constraints.maxWidth < 400;

                if (isNarrow) {
                  // 모바일: 목표/현재 수익 나란히, 달성률 다음 줄
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: _buildInfoColumn('총 목표 수익', '₩ ${NumberFormat('#,###').format(totalTarget)}'),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Consumer(
                              builder: (context, ref, child) {
                                final revenueStats = ref.watch(revenueGoalStatsProvider);
                                return _buildInfoColumn(
                                  '현재 수익',
                                  '₩ ${NumberFormat('#,###').format(revenueStats.totalActual.toInt())}'
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Consumer(
                        builder: (context, ref, child) {
                          final revenueStats = ref.watch(revenueGoalStatsProvider);
                          return _buildProgressInfo(revenueStats.achievementRate);
                        },
                      ),
                    ],
                  );
                } else {
                  // 태블릿: 기존 레이아웃
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildInfoColumn('총 목표 수익', '₩ ${NumberFormat('#,###').format(totalTarget)}'),
                      const SizedBox(height: 8),
                      Consumer(
                        builder: (context, ref, child) {
                          final revenueStats = ref.watch(revenueGoalStatsProvider);
                          return _buildInfoColumn(
                            '현재 수익',
                            '₩ ${NumberFormat('#,###').format(revenueStats.totalActual.toInt())}'
                          );
                        },
                      ),
                      const SizedBox(height: 8),
                      Consumer(
                        builder: (context, ref, child) {
                          final revenueStats = ref.watch(revenueGoalStatsProvider);
                          return _buildProgressInfo(revenueStats.achievementRate);
                        },
                      ),
                    ],
                  );
                }
              },
            ),
          ),
          const SizedBox(height: 16),

          // 날짜별 목표 입력 필드들
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: eventDates.length,
            itemBuilder: (context, index) {
              final date = eventDates[index];
              return _buildOverallDateGoalField(date, overallGoals);
            },
          ),
        ],
      ),
    );
  }

  /// 정보 컬럼 위젯
  Widget _buildInfoColumn(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: AppColors.secondaryTextColor,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.primaryTextColor,
          ),
        ),
      ],
    );
  }

  /// 진행률 정보 위젯
  Widget _buildProgressInfo(double progress) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '달성률',
          style: TextStyle(
            fontSize: 12,
            color: AppColors.secondaryTextColor,
          ),
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            Expanded(
              child: LinearProgressIndicator(
                value: progress,
                backgroundColor: AppColors.secondaryTextColor.withValues(alpha: 0.2),
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primarySeed),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              '${(progress * 100).toStringAsFixed(1)}%',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.primaryTextColor,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 전체 목표 날짜별 입력 필드
  Widget _buildOverallDateGoalField(DateTime date, List<RevenueGoal> overallGoals) {
    final dateStr = DateFormat('yyyy-MM-dd').format(date);
    final weekday = _getWeekdayName(date.weekday);

    final existingGoal = overallGoals.firstWhere(
      (g) => g.date == dateStr,
      orElse: () => RevenueGoal(
        eventId: ref.read(currentWorkspaceProvider)!.id,
        sellerId: null, // 전체 목표
        date: dateStr,
        targetAmount: 0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );

    if (!_controllers.containsKey('overall')) {
      _controllers['overall'] = {};
    }
    if (!_originalValues.containsKey('overall')) {
      _originalValues['overall'] = {};
    }
    if (!_controllers['overall']!.containsKey(dateStr)) {
      final originalValue = existingGoal.targetAmount > 0 ? NumberFormat('#,###').format(existingGoal.targetAmount.toInt()) : '';
      _controllers['overall']![dateStr] = TextEditingController(text: originalValue);
      _originalValues['overall']![dateStr] = originalValue;
    }

    final controller = _controllers['overall']![dateStr]!;

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              '${DateFormat('M/d').format(date)} ($weekday)',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: AppColors.primaryTextColor,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            flex: 3,
            child: TextFormField(
              controller: controller,
              keyboardType: TextInputType.number,
              textInputAction: TextInputAction.done,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                _ThousandsSeparatorInputFormatter(),
              ],
              decoration: InputDecoration(
                hintText: '목표 금액',
                suffixText: '원',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: AppColors.secondaryTextColor.withValues(alpha: 0.3)),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              style: const TextStyle(fontSize: 14),
              onChanged: (value) => _saveGoalImmediately('overall', dateStr, value),
            ),
          ),
        ],
      ),
    );
  }

  /// 판매자별 목표 목록 UI
  Widget _buildSellerGoalsList(List<Seller> sellers, List<DateTime> eventDates, List<RevenueGoal> goals) {
    // 디버그 로그 추가
    LoggerUtils.logDebug('판매자별 목표 목록 빌드: 판매자 ${sellers.length}개, 날짜 ${eventDates.length}개, 목표 ${goals.length}개', tag: 'RevenueGoalDialog');

    // 판매자별 목표 수익 매핑 디버그
    for (final seller in sellers) {
      final sellerId = seller.id?.toString() ?? '';
      final sellerGoals = goals.where((g) => g.sellerId == sellerId).toList();
      LoggerUtils.logDebug('판매자 ${seller.name} (ID: $sellerId): 목표 ${sellerGoals.length}개', tag: 'RevenueGoalDialog');
    }

    // 안전성 검사
    if (sellers.isEmpty) {
      LoggerUtils.logDebug('판매자가 없어서 빈 화면 표시', tag: 'RevenueGoalDialog');
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(32),
          child: Text(
            '등록된 판매자가 없습니다.\n판매자를 먼저 등록해주세요.',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.all(16),
      itemCount: sellers.length,
      itemBuilder: (context, index) {
        // 인덱스 범위 검사
        if (index >= sellers.length) {
          return const SizedBox.shrink();
        }

        final seller = sellers[index];
        final sellerId = seller.id?.toString() ?? '';

        // 빈 sellerId 처리
        if (sellerId.isEmpty) {
          return const SizedBox.shrink();
        }

        final isExpanded = _expandedSellers[sellerId] ?? false;
        final sellerGoals = goals.where((g) => g.sellerId == sellerId).toList();
        final totalTarget = sellerGoals.fold<double>(0, (sum, goal) => sum + goal.targetAmount);

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: Column(
            children: [
              // 판매자 헤더
              ListTile(
                leading: Icon(
                  Icons.person,
                  color: AppColors.primarySeed,
                ),
                title: Text(
                  seller.name.isNotEmpty ? seller.name : '이름 없음',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: AppColors.primaryTextColor,
                  ),
                ),
                subtitle: Text(
                  '총 목표 수익: ₩ ${NumberFormat('#,###').format(totalTarget)}',
                  style: TextStyle(color: AppColors.secondaryTextColor),
                ),
                trailing: IconButton(
                  icon: Icon(
                    isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: AppColors.secondaryTextColor,
                  ),
                  onPressed: () {
                    if (mounted) {
                      setState(() {
                        _expandedSellers[sellerId] = !isExpanded;
                      });
                    }
                  },
                ),
              ),

              // 확장된 날짜별 목표 입력 필드들
              if (isExpanded) ...[
                const Divider(height: 1),
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: _buildDateGoalsList(sellerId, eventDates, sellerGoals),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  /// 날짜별 목표 입력 필드 목록 (안전한 구현)
  Widget _buildDateGoalsList(String sellerId, List<DateTime> eventDates, List<RevenueGoal> sellerGoals) {
    // 안전성 검사
    if (eventDates.isEmpty) {
      return const Text('행사 날짜가 설정되지 않았습니다.');
    }

    // sellerId로 판매자 찾기
    final sellers = ref.watch(sellerNotifierProvider).sellers;
    final seller = sellers.firstWhere(
      (s) => s.id?.toString() == sellerId,
      orElse: () => Seller(
        id: int.tryParse(sellerId),
        name: '알 수 없는 판매자',
        eventId: ref.read(currentWorkspaceProvider)?.id ?? 1,
      ),
    );

    return Column(
      children: eventDates.map((date) {
        try {
          return _buildSellerDateGoalField(seller, date, sellerGoals);
        } catch (e) {
          LoggerUtils.logError('날짜별 목표 필드 생성 실패: $e', tag: 'RevenueGoalDialog');
          return Container(
            padding: const EdgeInsets.all(8),
            child: Text(
              '날짜 ${DateFormat('M/d').format(date)} 필드 로딩 실패',
              style: const TextStyle(color: Colors.red),
            ),
          );
        }
      }).toList(),
    );
  }

  /// 판매자별 날짜 목표 입력 필드
  Widget _buildSellerDateGoalField(Seller seller, DateTime date, List<RevenueGoal> sellerGoals) {
    final dateStr = DateFormat('yyyy-MM-dd').format(date);
    final weekday = _getWeekdayName(date.weekday);
    final sellerId = seller.id?.toString() ?? '';

    final existingGoal = sellerGoals.firstWhere(
      (g) => g.date == dateStr,
      orElse: () => RevenueGoal(
        eventId: seller.eventId,
        sellerId: sellerId,
        date: dateStr,
        targetAmount: 0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );

    if (!_controllers.containsKey(sellerId)) {
      _controllers[sellerId] = {};
    }
    if (!_originalValues.containsKey(sellerId)) {
      _originalValues[sellerId] = {};
    }
    if (!_controllers[sellerId]!.containsKey(dateStr)) {
      final originalValue = existingGoal.targetAmount > 0 ? NumberFormat('#,###').format(existingGoal.targetAmount.toInt()) : '';
      _controllers[sellerId]![dateStr] = TextEditingController(text: originalValue);
      _originalValues[sellerId]![dateStr] = originalValue;
    }

    final controller = _controllers[sellerId]![dateStr]!;

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              '${DateFormat('M/d').format(date)} ($weekday)',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: AppColors.primaryTextColor,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            flex: 3,
            child: TextFormField(
              controller: controller,
              keyboardType: TextInputType.number,
              textInputAction: TextInputAction.done,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                _ThousandsSeparatorInputFormatter(),
              ],
              decoration: InputDecoration(
                hintText: '목표 금액',
                suffixText: '원',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: AppColors.secondaryTextColor.withValues(alpha: 0.3)),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              style: const TextStyle(fontSize: 14),
              onChanged: (value) => _saveGoalImmediately(sellerId, dateStr, value),
            ),
          ),
        ],
      ),
    );
  }

  /// 뒤로가기 처리: 개별 항목이 바로바로 저장되므로 바로 나가기
  Future<void> _onBackPressed() async {
    Navigator.of(context).pop();
  }





  /// 개별 목표 수익 즉시 저장
  Future<void> _saveGoalImmediately(String sellerId, String dateStr, String value) async {
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) return;

    final revenueGoalNotifier = ref.read(revenueGoalNotifierProvider.notifier);
    final amountText = value.replaceAll(',', '');

    try {
      if (amountText.isNotEmpty) {
        final amount = double.tryParse(amountText) ?? 0;

        if (amount > 0) {
          // 기존 목표 찾기
          final existingGoals = ref.read(revenueGoalNotifierProvider).goals
              .where((g) => g.eventId == currentWorkspace.id)
              .toList();

          final existing = existingGoals.firstWhere(
            (g) => (g.sellerId ?? 'overall') == sellerId && g.date == dateStr,
            orElse: () => RevenueGoal(
              eventId: currentWorkspace.id,
              sellerId: sellerId == 'overall' ? null : sellerId,
              date: dateStr,
              targetAmount: 0,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
          );

          if (existing.id != null) {
            // 업데이트
            final updated = existing.copyWith(
              targetAmount: amount,
              updatedAt: DateTime.now(),
            );
            await revenueGoalNotifier.updateGoal(updated);
          } else {
            // 새로 추가
            final goal = RevenueGoal(
              eventId: currentWorkspace.id,
              sellerId: sellerId == 'overall' ? null : sellerId,
              date: dateStr,
              targetAmount: amount,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            );
            await revenueGoalNotifier.addGoal(goal);
          }
        }
      }
    } catch (e) {
      // 에러 발생 시 조용히 처리 (사용자 입력 중 방해하지 않음)
      LoggerUtils.logError('목표 수익 즉시 저장 실패: sellerId=$sellerId, date=$dateStr', error: e);
    }
  }

  /// 요일 이름 가져오기
  String _getWeekdayName(int weekday) {
    const weekdays = ['월', '화', '수', '목', '금', '토', '일'];
    return weekdays[weekday - 1];
  }

  /// 행사 날짜 범위 가져오기
  List<DateTime> _getEventDateRange(dynamic workspace) {
    final startDate = workspace.startDate ?? DateTime.now();
    final endDate = workspace.endDate ?? DateTime.now();

    final dates = <DateTime>[];
    var current = startDate;

    while (current.isBefore(endDate) || current.isAtSameMomentAs(endDate)) {
      dates.add(current);
      current = current.add(const Duration(days: 1));
    }

    return dates;
  }
}

/// 천 단위 구분자 입력 포맷터
class _ThousandsSeparatorInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    if (newValue.text.isEmpty) {
      return newValue;
    }

    final number = int.tryParse(newValue.text.replaceAll(',', ''));
    if (number == null) {
      return oldValue;
    }

    final formatted = NumberFormat('#,###').format(number);
    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}
