import 'package:flutter/material.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
import '../utils/app_colors.dart';
import '../utils/dialog_theme.dart' as custom_dialog;

/// 튜토리얼 다이얼로그 - 유튜브 영상과 텍스트 설명 포함
class TutorialDialog extends StatefulWidget {
  final String title;
  final String youtubeVideoId;
  final String description;

  const TutorialDialog({
    super.key,
    required this.title,
    required this.youtubeVideoId,
    required this.description,
  });

  @override
  State<TutorialDialog> createState() => _TutorialDialogState();

  /// 튜토리얼 다이얼로그 표시
  static Future<void> show({
    required BuildContext context,
    required String title,
    required String youtubeVideoId,
    required String description,
  }) {
    return showGeneralDialog<void>(
      context: context,
      barrierDismissible: true,
      barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
      barrierColor: Colors.black.withValues(alpha: 0.5),
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (context, animation, secondaryAnimation) => TutorialDialog(
        title: title,
        youtubeVideoId: youtubeVideoId,
        description: description,
      ),
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        // 페이드 인 + 아래에서 위로 슬라이드 애니메이션
        return FadeTransition(
          opacity: animation,
          child: SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0, 0.3), // 아래에서 시작
              end: Offset.zero, // 원래 위치로
            ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.easeOutCubic,
            )),
            child: child,
          ),
        );
      },
    );
  }
}

class _TutorialDialogState extends State<TutorialDialog> {
  late YoutubePlayerController _controller;
  bool _isPlayerReady = false;

  @override
  void initState() {
    super.initState();
    _controller = YoutubePlayerController(
      initialVideoId: widget.youtubeVideoId,
      flags: const YoutubePlayerFlags(
        mute: false,
        autoPlay: false,
        disableDragSeek: false,
        loop: false,
        isLive: false,
        forceHD: false,
        enableCaption: true,
      ),
    );
    _controller.addListener(_listener);
  }

  void _listener() {
    if (_isPlayerReady && mounted && _controller.value.isReady) {
      setState(() {});
    }
  }

  @override
  void dispose() {
    _controller.removeListener(_listener);
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width > 600;
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;

    // 다이얼로그 크기 계산 (거의 화면 전체)
    final dialogWidth = size.width * (isLandscape ? 0.9 : 0.95);
    final dialogHeight = size.height * (isLandscape ? 0.9 : 0.9);

    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.all(isLandscape ? 8 : 16),
      child: Container(
        width: dialogWidth,
        height: dialogHeight,
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          children: [
            // 헤더
            Container(
              padding: EdgeInsets.all(isTablet ? 20 : 16),
              decoration: BoxDecoration(
                color: AppColors.primarySeed,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.play_circle_outline,
                    color: Colors.white,
                    size: isTablet ? 28 : 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      widget.title,
                      style: TextStyle(
                        fontSize: isTablet ? 20 : 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        fontFamily: 'Pretendard',
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(
                      Icons.close,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),

            // 내용 (스크롤 가능)
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(isTablet ? 24 : 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 유튜브 플레이어
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: YoutubePlayerBuilder(
                          onExitFullScreen: () {
                            // 전체화면 종료 시 처리
                          },
                          player: YoutubePlayer(
                            controller: _controller,
                            showVideoProgressIndicator: true,
                            onReady: () {
                              _isPlayerReady = true;
                            },
                            onEnded: (data) {
                              // 영상 종료 시 처리
                            },
                          ),
                          builder: (context, player) => player,
                        ),
                      ),
                    ),

                    SizedBox(height: isTablet ? 32 : 24),

                    // 설명 텍스트
                    Text(
                      '사용 방법',
                      style: TextStyle(
                        fontSize: isTablet ? 20 : 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.onSurface,
                        fontFamily: 'Pretendard',
                      ),
                    ),

                    SizedBox(height: isTablet ? 16 : 12),

                    Text(
                      widget.description,
                      style: TextStyle(
                        fontSize: isTablet ? 16 : 14,
                        color: AppColors.onSurface,
                        height: 1.6,
                        fontFamily: 'Pretendard',
                      ),
                    ),

                    SizedBox(height: isTablet ? 32 : 24),

                    // 하단 버튼
                    Center(
                      child: custom_dialog.DialogTheme.buildModernButton(
                        text: '확인',
                        onPressed: () => Navigator.of(context).pop(),
                        isTablet: isTablet,
                        isPrimary: true,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
