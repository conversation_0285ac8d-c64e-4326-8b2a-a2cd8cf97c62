/// 바라 부스 매니저 - 전화번호 인증 위젯
///
/// 구독 관리 페이지에 임베드 가능한 전화번호 인증 위젯입니다.
/// - SMS 인증번호 발송
/// - 인증번호 확인
/// - 실시간 상태 업데이트
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 1월
library;

import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:http/http.dart' as http;
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import '../utils/logger_utils.dart';
import '../utils/toast_utils.dart';
import '../utils/app_colors.dart';
import '../services/phone_verification_limit_service.dart';

/// 전화번호 인증 위젯
class PhoneVerificationWidget extends StatefulWidget {
  /// 인증 완료 콜백
  final VoidCallback? onVerificationComplete;
  
  /// 컴팩트 모드 (작은 공간에 맞춤)
  final bool isCompact;

  const PhoneVerificationWidget({
    super.key,
    this.onVerificationComplete,
    this.isCompact = false,
  });

  @override
  State<PhoneVerificationWidget> createState() => _PhoneVerificationWidgetState();
}

class _PhoneVerificationWidgetState extends State<PhoneVerificationWidget> {
  static const String _tag = 'PhoneVerificationWidget';
  
  // 컨트롤러
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _codeController = TextEditingController();

  // 전화번호 마스킹 포맷터
  final MaskTextInputFormatter _phoneMaskFormatter = MaskTextInputFormatter(
    mask: '###-####-####',
    filter: {"#": RegExp(r'[0-9]')},
    type: MaskAutoCompletionType.lazy,
  );

  // 상태 변수
  bool _isLoading = false;
  bool _isCodeSent = false;
  bool _isVerifying = false;
  String? _errorMessage;
  String? _successMessage;
  
  // 카운트다운
  Timer? _countdownTimer;
  int _countdownSeconds = 300; // 5분

  // 현재 사용자 UID
  String? get _currentUserUID => FirebaseAuth.instance.currentUser?.uid;

  @override
  void dispose() {
    _phoneController.dispose();
    _codeController.dispose();
    _countdownTimer?.cancel();
    super.dispose();
  }

  /// 인증번호 발송
  Future<void> _sendVerificationCode() async {
    if (_isLoading) return;
    
    final phoneNumber = _phoneController.text.trim();
    if (phoneNumber.isEmpty) {
      _showError('전화번호를 입력해주세요.');
      return;
    }
    
    // 전화번호 형식 검증
    final phoneRegex = RegExp(r'^010-\d{4}-\d{4}$');
    if (!phoneRegex.hasMatch(phoneNumber)) {
      _showError('올바른 전화번호 형식으로 입력해주세요. (010-1234-5678)');
      return;
    }
    
    // 클라이언트 측 제한 확인
    final limitCheck = await PhoneVerificationLimitService.checkAllLimits(phoneNumber);
    if (!limitCheck['canSend']) {
      _showError(limitCheck['reason']);
      return;
    }
    
    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _successMessage = null;
    });
    
    try {
      // 전화번호 중복 확인
      await _checkPhoneDuplicate(phoneNumber);
      
      // SMS 발송 요청
      final response = await http.post(
        Uri.parse('https://us-central1-parabara-1a504.cloudfunctions.net/sendSMSRequest'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'phoneNumber': phoneNumber,
          'recaptchaToken': 'app_check_verified',
          'uid': _currentUserUID,
        }),
      );
      
      final result = json.decode(response.body);
      
      if (response.statusCode == 200 && result['success'] == true) {
        setState(() {
          _isCodeSent = true;
          _successMessage = '인증번호가 발송되었습니다.';
        });
        _startCountdown();
        LoggerUtils.logInfo('SMS 발송 성공: $phoneNumber', tag: _tag);
      } else {
        throw Exception(result['message'] ?? 'SMS 발송에 실패했습니다.');
      }
    } catch (e) {
      LoggerUtils.logError('SMS 발송 실패', tag: _tag, error: e);
      String errorMessage = e.toString().replaceAll('Exception: ', '');
      // axios 에러가 포함된 경우 더 자세한 정보 표시
      if (errorMessage.contains('axios')) {
        errorMessage = 'SMS 서비스 연결 오류가 발생했습니다. 네트워크 상태를 확인해주세요.\n상세 오류: $errorMessage';
      }
      _showError(errorMessage);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 인증번호 확인
  Future<void> _verifyCode() async {
    if (_isVerifying) return;
    
    final phoneNumber = _phoneController.text.trim();
    final code = _codeController.text.trim();
    
    if (code.isEmpty) {
      _showError('인증번호를 입력해주세요.');
      return;
    }
    
    if (code.length != 6) {
      _showError('인증번호는 6자리입니다.');
      return;
    }
    
    setState(() {
      _isVerifying = true;
      _errorMessage = null;
    });
    
    try {
      final response = await http.post(
        Uri.parse('https://us-central1-parabara-1a504.cloudfunctions.net/verifySMSRequest'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'phoneNumber': phoneNumber,
          'code': code,
          'uid': _currentUserUID,
        }),
      );
      
      final result = json.decode(response.body);
      
      if (response.statusCode == 200 && result['success'] == true) {
        // 인증 성공
        LoggerUtils.logInfo('전화번호 인증 완료: $phoneNumber', tag: _tag);
        
        setState(() {
          _successMessage = '전화번호 인증이 완료되었습니다!';
        });
        
        // 콜백 호출
        widget.onVerificationComplete?.call();
        
        // 성공 토스트
        if (mounted) {
          ToastUtils.showSuccess(context, '전화번호 인증이 완료되었습니다!');
        }
      } else {
        throw Exception(result['message'] ?? '인증에 실패했습니다.');
      }
    } catch (e) {
      LoggerUtils.logError('인증 확인 실패', tag: _tag, error: e);
      String errorMessage = e.toString().replaceAll('Exception: ', '');
      // axios 에러가 포함된 경우 더 자세한 정보 표시
      if (errorMessage.contains('axios')) {
        errorMessage = 'SMS 인증 서비스 연결 오류가 발생했습니다. 네트워크 상태를 확인해주세요.\n상세 오류: $errorMessage';
      }
      _showError(errorMessage);
    } finally {
      setState(() => _isVerifying = false);
    }
  }

  /// 전화번호 중복 확인
  Future<void> _checkPhoneDuplicate(String phoneNumber) async {
    final querySnapshot = await FirebaseFirestore.instance
        .collection('users')
        .where('phone', isEqualTo: phoneNumber)
        .where('phoneVerified', isEqualTo: true)
        .get();

    // 현재 사용자가 아닌 다른 사용자가 이미 이 번호를 사용 중인지 확인
    final duplicateUser = querySnapshot.docs
        .where((doc) => doc.id != _currentUserUID)
        .firstOrNull;

    if (duplicateUser != null) {
      throw Exception('이미 다른 계정에서 사용 중인 전화번호입니다.');
    }
  }

  /// 카운트다운 시작
  void _startCountdown() {
    _countdownSeconds = 300; // 5분
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_countdownSeconds > 0) {
          _countdownSeconds--;
        } else {
          timer.cancel();
        }
      });
    });
  }

  /// 에러 메시지 표시
  void _showError(String message) {
    setState(() {
      _errorMessage = message;
      _successMessage = null;
    });
  }

  /// 카운트다운 포맷팅
  String _formatCountdown(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(widget.isCompact ? 16 : 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 헤더
            Row(
              children: [
                Icon(
                  Icons.phone_android,
                  color: AppColors.primarySeed,
                  size: widget.isCompact ? 20 : 24,
                ),
                SizedBox(width: widget.isCompact ? 8 : 12),
                Expanded(
                  child: Text(
                    '전화번호 인증',
                    style: TextStyle(
                      fontSize: widget.isCompact ? 16 : 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: widget.isCompact ? 8 : 12),
            
            // 안내 텍스트
            Text(
              '구독 서비스 이용을 위해 본인 확인이 필요합니다.',
              style: TextStyle(
                fontSize: widget.isCompact ? 13 : 14,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: widget.isCompact ? 16 : 20),
            
            // 전화번호 입력 + 발송 버튼
            if (!_isCodeSent)
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _phoneController,
                      inputFormatters: [_phoneMaskFormatter],
                      keyboardType: TextInputType.phone,
                      decoration: InputDecoration(
                        labelText: '전화번호',
                        hintText: '010-1234-5678',
                        prefixIcon: const Icon(Icons.phone),
                        border: const OutlineInputBorder(),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton(
                    onPressed: _isLoading ? null : _sendVerificationCode,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primarySeed,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(
                        horizontal: widget.isCompact ? 16 : 20,
                        vertical: widget.isCompact ? 16 : 20,
                      ),
                    ),
                    child: _isLoading
                        ? SizedBox(
                            height: 16,
                            width: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(
                            '발송',
                            style: TextStyle(fontSize: widget.isCompact ? 13 : 14),
                          ),
                  ),
                ],
              )
            else
              // 전화번호 입력 (비활성화 상태)
              TextField(
                controller: _phoneController,
                inputFormatters: [_phoneMaskFormatter],
                keyboardType: TextInputType.phone,
                enabled: false,
                decoration: InputDecoration(
                  labelText: '전화번호',
                  hintText: '010-1234-5678',
                  prefixIcon: const Icon(Icons.phone),
                  border: const OutlineInputBorder(),
                ),
              ),
            SizedBox(height: widget.isCompact ? 12 : 16),
            
            // 인증번호 입력 및 확인
            if (_isCodeSent) ...[
              TextField(
                controller: _codeController,
                keyboardType: TextInputType.number,
                maxLength: 6,
                decoration: InputDecoration(
                  labelText: '인증번호',
                  hintText: '6자리 숫자 입력',
                  prefixIcon: const Icon(Icons.security),
                  border: const OutlineInputBorder(),
                  counterText: '남은 시간: ${_formatCountdown(_countdownSeconds)}',
                ),
              ),
              SizedBox(height: widget.isCompact ? 12 : 16),
              
              ElevatedButton(
                onPressed: _isVerifying ? null : _verifyCode,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primarySeed,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: widget.isCompact ? 12 : 16),
                ),
                child: _isVerifying
                    ? SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        '인증 확인',
                        style: TextStyle(fontSize: widget.isCompact ? 14 : 16),
                      ),
              ),
            ],
            
            // 메시지 표시
            if (_errorMessage != null || _successMessage != null) ...[
              SizedBox(height: widget.isCompact ? 8 : 12),
              Container(
                padding: EdgeInsets.all(widget.isCompact ? 8 : 12),
                decoration: BoxDecoration(
                  color: _errorMessage != null ? Colors.red.shade50 : Colors.green.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: _errorMessage != null ? Colors.red.shade200 : Colors.green.shade200,
                  ),
                ),
                child: Text(
                  _errorMessage ?? _successMessage ?? '',
                  style: TextStyle(
                    color: _errorMessage != null ? Colors.red.shade700 : Colors.green.shade700,
                    fontSize: widget.isCompact ? 12 : 13,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
