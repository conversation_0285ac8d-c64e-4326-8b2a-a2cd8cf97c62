// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'subscription_plan.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SubscriptionPlan {

/// 플랜 타입
 SubscriptionPlanType get type;/// 플랜 이름
 String get name;/// 플랜 설명
 String get description;/// 월 가격 (원)
 int get monthlyPrice;/// 연 가격 (원)
 int get yearlyPrice;/// 최대 행사 수 (null이면 무제한)
 int? get maxEvents;/// 최대 상품 수 (null이면 무제한)
 int? get maxProducts;/// 세트 할인 기능 사용 가능 여부
 bool get hasSetDiscountFeature;/// 서비스 기능 사용 가능 여부
 bool get hasServiceFeature;/// 서버 연동 기능 사용 가능 여부
 bool get hasServerSyncFeature;/// 판매자별 관리 기능 사용 가능 여부
 bool get hasSellerManagementFeature;/// 엑셀 내보내기 기능 사용 가능 여부
 bool get hasExcelExportFeature;/// PDF 내보내기 기능 사용 가능 여부
 bool get hasPdfExportFeature;/// 고급 통계 기능 사용 가능 여부
 bool get hasAdvancedStatsFeature;/// 구글 드라이브 백업 기능 사용 가능 여부
 bool get hasGoogleDriveBackupFeature;
/// Create a copy of SubscriptionPlan
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SubscriptionPlanCopyWith<SubscriptionPlan> get copyWith => _$SubscriptionPlanCopyWithImpl<SubscriptionPlan>(this as SubscriptionPlan, _$identity);

  /// Serializes this SubscriptionPlan to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SubscriptionPlan&&(identical(other.type, type) || other.type == type)&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&(identical(other.monthlyPrice, monthlyPrice) || other.monthlyPrice == monthlyPrice)&&(identical(other.yearlyPrice, yearlyPrice) || other.yearlyPrice == yearlyPrice)&&(identical(other.maxEvents, maxEvents) || other.maxEvents == maxEvents)&&(identical(other.maxProducts, maxProducts) || other.maxProducts == maxProducts)&&(identical(other.hasSetDiscountFeature, hasSetDiscountFeature) || other.hasSetDiscountFeature == hasSetDiscountFeature)&&(identical(other.hasServiceFeature, hasServiceFeature) || other.hasServiceFeature == hasServiceFeature)&&(identical(other.hasServerSyncFeature, hasServerSyncFeature) || other.hasServerSyncFeature == hasServerSyncFeature)&&(identical(other.hasSellerManagementFeature, hasSellerManagementFeature) || other.hasSellerManagementFeature == hasSellerManagementFeature)&&(identical(other.hasExcelExportFeature, hasExcelExportFeature) || other.hasExcelExportFeature == hasExcelExportFeature)&&(identical(other.hasPdfExportFeature, hasPdfExportFeature) || other.hasPdfExportFeature == hasPdfExportFeature)&&(identical(other.hasAdvancedStatsFeature, hasAdvancedStatsFeature) || other.hasAdvancedStatsFeature == hasAdvancedStatsFeature)&&(identical(other.hasGoogleDriveBackupFeature, hasGoogleDriveBackupFeature) || other.hasGoogleDriveBackupFeature == hasGoogleDriveBackupFeature));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,type,name,description,monthlyPrice,yearlyPrice,maxEvents,maxProducts,hasSetDiscountFeature,hasServiceFeature,hasServerSyncFeature,hasSellerManagementFeature,hasExcelExportFeature,hasPdfExportFeature,hasAdvancedStatsFeature,hasGoogleDriveBackupFeature);

@override
String toString() {
  return 'SubscriptionPlan(type: $type, name: $name, description: $description, monthlyPrice: $monthlyPrice, yearlyPrice: $yearlyPrice, maxEvents: $maxEvents, maxProducts: $maxProducts, hasSetDiscountFeature: $hasSetDiscountFeature, hasServiceFeature: $hasServiceFeature, hasServerSyncFeature: $hasServerSyncFeature, hasSellerManagementFeature: $hasSellerManagementFeature, hasExcelExportFeature: $hasExcelExportFeature, hasPdfExportFeature: $hasPdfExportFeature, hasAdvancedStatsFeature: $hasAdvancedStatsFeature, hasGoogleDriveBackupFeature: $hasGoogleDriveBackupFeature)';
}


}

/// @nodoc
abstract mixin class $SubscriptionPlanCopyWith<$Res>  {
  factory $SubscriptionPlanCopyWith(SubscriptionPlan value, $Res Function(SubscriptionPlan) _then) = _$SubscriptionPlanCopyWithImpl;
@useResult
$Res call({
 SubscriptionPlanType type, String name, String description, int monthlyPrice, int yearlyPrice, int? maxEvents, int? maxProducts, bool hasSetDiscountFeature, bool hasServiceFeature, bool hasServerSyncFeature, bool hasSellerManagementFeature, bool hasExcelExportFeature, bool hasPdfExportFeature, bool hasAdvancedStatsFeature, bool hasGoogleDriveBackupFeature
});




}
/// @nodoc
class _$SubscriptionPlanCopyWithImpl<$Res>
    implements $SubscriptionPlanCopyWith<$Res> {
  _$SubscriptionPlanCopyWithImpl(this._self, this._then);

  final SubscriptionPlan _self;
  final $Res Function(SubscriptionPlan) _then;

/// Create a copy of SubscriptionPlan
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? type = null,Object? name = null,Object? description = null,Object? monthlyPrice = null,Object? yearlyPrice = null,Object? maxEvents = freezed,Object? maxProducts = freezed,Object? hasSetDiscountFeature = null,Object? hasServiceFeature = null,Object? hasServerSyncFeature = null,Object? hasSellerManagementFeature = null,Object? hasExcelExportFeature = null,Object? hasPdfExportFeature = null,Object? hasAdvancedStatsFeature = null,Object? hasGoogleDriveBackupFeature = null,}) {
  return _then(_self.copyWith(
type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as SubscriptionPlanType,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,monthlyPrice: null == monthlyPrice ? _self.monthlyPrice : monthlyPrice // ignore: cast_nullable_to_non_nullable
as int,yearlyPrice: null == yearlyPrice ? _self.yearlyPrice : yearlyPrice // ignore: cast_nullable_to_non_nullable
as int,maxEvents: freezed == maxEvents ? _self.maxEvents : maxEvents // ignore: cast_nullable_to_non_nullable
as int?,maxProducts: freezed == maxProducts ? _self.maxProducts : maxProducts // ignore: cast_nullable_to_non_nullable
as int?,hasSetDiscountFeature: null == hasSetDiscountFeature ? _self.hasSetDiscountFeature : hasSetDiscountFeature // ignore: cast_nullable_to_non_nullable
as bool,hasServiceFeature: null == hasServiceFeature ? _self.hasServiceFeature : hasServiceFeature // ignore: cast_nullable_to_non_nullable
as bool,hasServerSyncFeature: null == hasServerSyncFeature ? _self.hasServerSyncFeature : hasServerSyncFeature // ignore: cast_nullable_to_non_nullable
as bool,hasSellerManagementFeature: null == hasSellerManagementFeature ? _self.hasSellerManagementFeature : hasSellerManagementFeature // ignore: cast_nullable_to_non_nullable
as bool,hasExcelExportFeature: null == hasExcelExportFeature ? _self.hasExcelExportFeature : hasExcelExportFeature // ignore: cast_nullable_to_non_nullable
as bool,hasPdfExportFeature: null == hasPdfExportFeature ? _self.hasPdfExportFeature : hasPdfExportFeature // ignore: cast_nullable_to_non_nullable
as bool,hasAdvancedStatsFeature: null == hasAdvancedStatsFeature ? _self.hasAdvancedStatsFeature : hasAdvancedStatsFeature // ignore: cast_nullable_to_non_nullable
as bool,hasGoogleDriveBackupFeature: null == hasGoogleDriveBackupFeature ? _self.hasGoogleDriveBackupFeature : hasGoogleDriveBackupFeature // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [SubscriptionPlan].
extension SubscriptionPlanPatterns on SubscriptionPlan {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SubscriptionPlan value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SubscriptionPlan() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SubscriptionPlan value)  $default,){
final _that = this;
switch (_that) {
case _SubscriptionPlan():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SubscriptionPlan value)?  $default,){
final _that = this;
switch (_that) {
case _SubscriptionPlan() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( SubscriptionPlanType type,  String name,  String description,  int monthlyPrice,  int yearlyPrice,  int? maxEvents,  int? maxProducts,  bool hasSetDiscountFeature,  bool hasServiceFeature,  bool hasServerSyncFeature,  bool hasSellerManagementFeature,  bool hasExcelExportFeature,  bool hasPdfExportFeature,  bool hasAdvancedStatsFeature,  bool hasGoogleDriveBackupFeature)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SubscriptionPlan() when $default != null:
return $default(_that.type,_that.name,_that.description,_that.monthlyPrice,_that.yearlyPrice,_that.maxEvents,_that.maxProducts,_that.hasSetDiscountFeature,_that.hasServiceFeature,_that.hasServerSyncFeature,_that.hasSellerManagementFeature,_that.hasExcelExportFeature,_that.hasPdfExportFeature,_that.hasAdvancedStatsFeature,_that.hasGoogleDriveBackupFeature);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( SubscriptionPlanType type,  String name,  String description,  int monthlyPrice,  int yearlyPrice,  int? maxEvents,  int? maxProducts,  bool hasSetDiscountFeature,  bool hasServiceFeature,  bool hasServerSyncFeature,  bool hasSellerManagementFeature,  bool hasExcelExportFeature,  bool hasPdfExportFeature,  bool hasAdvancedStatsFeature,  bool hasGoogleDriveBackupFeature)  $default,) {final _that = this;
switch (_that) {
case _SubscriptionPlan():
return $default(_that.type,_that.name,_that.description,_that.monthlyPrice,_that.yearlyPrice,_that.maxEvents,_that.maxProducts,_that.hasSetDiscountFeature,_that.hasServiceFeature,_that.hasServerSyncFeature,_that.hasSellerManagementFeature,_that.hasExcelExportFeature,_that.hasPdfExportFeature,_that.hasAdvancedStatsFeature,_that.hasGoogleDriveBackupFeature);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( SubscriptionPlanType type,  String name,  String description,  int monthlyPrice,  int yearlyPrice,  int? maxEvents,  int? maxProducts,  bool hasSetDiscountFeature,  bool hasServiceFeature,  bool hasServerSyncFeature,  bool hasSellerManagementFeature,  bool hasExcelExportFeature,  bool hasPdfExportFeature,  bool hasAdvancedStatsFeature,  bool hasGoogleDriveBackupFeature)?  $default,) {final _that = this;
switch (_that) {
case _SubscriptionPlan() when $default != null:
return $default(_that.type,_that.name,_that.description,_that.monthlyPrice,_that.yearlyPrice,_that.maxEvents,_that.maxProducts,_that.hasSetDiscountFeature,_that.hasServiceFeature,_that.hasServerSyncFeature,_that.hasSellerManagementFeature,_that.hasExcelExportFeature,_that.hasPdfExportFeature,_that.hasAdvancedStatsFeature,_that.hasGoogleDriveBackupFeature);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SubscriptionPlan implements SubscriptionPlan {
  const _SubscriptionPlan({required this.type, required this.name, required this.description, required this.monthlyPrice, required this.yearlyPrice, this.maxEvents, this.maxProducts, this.hasSetDiscountFeature = false, this.hasServiceFeature = false, this.hasServerSyncFeature = false, this.hasSellerManagementFeature = false, this.hasExcelExportFeature = false, this.hasPdfExportFeature = false, this.hasAdvancedStatsFeature = false, this.hasGoogleDriveBackupFeature = false});
  factory _SubscriptionPlan.fromJson(Map<String, dynamic> json) => _$SubscriptionPlanFromJson(json);

/// 플랜 타입
@override final  SubscriptionPlanType type;
/// 플랜 이름
@override final  String name;
/// 플랜 설명
@override final  String description;
/// 월 가격 (원)
@override final  int monthlyPrice;
/// 연 가격 (원)
@override final  int yearlyPrice;
/// 최대 행사 수 (null이면 무제한)
@override final  int? maxEvents;
/// 최대 상품 수 (null이면 무제한)
@override final  int? maxProducts;
/// 세트 할인 기능 사용 가능 여부
@override@JsonKey() final  bool hasSetDiscountFeature;
/// 서비스 기능 사용 가능 여부
@override@JsonKey() final  bool hasServiceFeature;
/// 서버 연동 기능 사용 가능 여부
@override@JsonKey() final  bool hasServerSyncFeature;
/// 판매자별 관리 기능 사용 가능 여부
@override@JsonKey() final  bool hasSellerManagementFeature;
/// 엑셀 내보내기 기능 사용 가능 여부
@override@JsonKey() final  bool hasExcelExportFeature;
/// PDF 내보내기 기능 사용 가능 여부
@override@JsonKey() final  bool hasPdfExportFeature;
/// 고급 통계 기능 사용 가능 여부
@override@JsonKey() final  bool hasAdvancedStatsFeature;
/// 구글 드라이브 백업 기능 사용 가능 여부
@override@JsonKey() final  bool hasGoogleDriveBackupFeature;

/// Create a copy of SubscriptionPlan
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SubscriptionPlanCopyWith<_SubscriptionPlan> get copyWith => __$SubscriptionPlanCopyWithImpl<_SubscriptionPlan>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SubscriptionPlanToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SubscriptionPlan&&(identical(other.type, type) || other.type == type)&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&(identical(other.monthlyPrice, monthlyPrice) || other.monthlyPrice == monthlyPrice)&&(identical(other.yearlyPrice, yearlyPrice) || other.yearlyPrice == yearlyPrice)&&(identical(other.maxEvents, maxEvents) || other.maxEvents == maxEvents)&&(identical(other.maxProducts, maxProducts) || other.maxProducts == maxProducts)&&(identical(other.hasSetDiscountFeature, hasSetDiscountFeature) || other.hasSetDiscountFeature == hasSetDiscountFeature)&&(identical(other.hasServiceFeature, hasServiceFeature) || other.hasServiceFeature == hasServiceFeature)&&(identical(other.hasServerSyncFeature, hasServerSyncFeature) || other.hasServerSyncFeature == hasServerSyncFeature)&&(identical(other.hasSellerManagementFeature, hasSellerManagementFeature) || other.hasSellerManagementFeature == hasSellerManagementFeature)&&(identical(other.hasExcelExportFeature, hasExcelExportFeature) || other.hasExcelExportFeature == hasExcelExportFeature)&&(identical(other.hasPdfExportFeature, hasPdfExportFeature) || other.hasPdfExportFeature == hasPdfExportFeature)&&(identical(other.hasAdvancedStatsFeature, hasAdvancedStatsFeature) || other.hasAdvancedStatsFeature == hasAdvancedStatsFeature)&&(identical(other.hasGoogleDriveBackupFeature, hasGoogleDriveBackupFeature) || other.hasGoogleDriveBackupFeature == hasGoogleDriveBackupFeature));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,type,name,description,monthlyPrice,yearlyPrice,maxEvents,maxProducts,hasSetDiscountFeature,hasServiceFeature,hasServerSyncFeature,hasSellerManagementFeature,hasExcelExportFeature,hasPdfExportFeature,hasAdvancedStatsFeature,hasGoogleDriveBackupFeature);

@override
String toString() {
  return 'SubscriptionPlan(type: $type, name: $name, description: $description, monthlyPrice: $monthlyPrice, yearlyPrice: $yearlyPrice, maxEvents: $maxEvents, maxProducts: $maxProducts, hasSetDiscountFeature: $hasSetDiscountFeature, hasServiceFeature: $hasServiceFeature, hasServerSyncFeature: $hasServerSyncFeature, hasSellerManagementFeature: $hasSellerManagementFeature, hasExcelExportFeature: $hasExcelExportFeature, hasPdfExportFeature: $hasPdfExportFeature, hasAdvancedStatsFeature: $hasAdvancedStatsFeature, hasGoogleDriveBackupFeature: $hasGoogleDriveBackupFeature)';
}


}

/// @nodoc
abstract mixin class _$SubscriptionPlanCopyWith<$Res> implements $SubscriptionPlanCopyWith<$Res> {
  factory _$SubscriptionPlanCopyWith(_SubscriptionPlan value, $Res Function(_SubscriptionPlan) _then) = __$SubscriptionPlanCopyWithImpl;
@override @useResult
$Res call({
 SubscriptionPlanType type, String name, String description, int monthlyPrice, int yearlyPrice, int? maxEvents, int? maxProducts, bool hasSetDiscountFeature, bool hasServiceFeature, bool hasServerSyncFeature, bool hasSellerManagementFeature, bool hasExcelExportFeature, bool hasPdfExportFeature, bool hasAdvancedStatsFeature, bool hasGoogleDriveBackupFeature
});




}
/// @nodoc
class __$SubscriptionPlanCopyWithImpl<$Res>
    implements _$SubscriptionPlanCopyWith<$Res> {
  __$SubscriptionPlanCopyWithImpl(this._self, this._then);

  final _SubscriptionPlan _self;
  final $Res Function(_SubscriptionPlan) _then;

/// Create a copy of SubscriptionPlan
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? type = null,Object? name = null,Object? description = null,Object? monthlyPrice = null,Object? yearlyPrice = null,Object? maxEvents = freezed,Object? maxProducts = freezed,Object? hasSetDiscountFeature = null,Object? hasServiceFeature = null,Object? hasServerSyncFeature = null,Object? hasSellerManagementFeature = null,Object? hasExcelExportFeature = null,Object? hasPdfExportFeature = null,Object? hasAdvancedStatsFeature = null,Object? hasGoogleDriveBackupFeature = null,}) {
  return _then(_SubscriptionPlan(
type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as SubscriptionPlanType,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,monthlyPrice: null == monthlyPrice ? _self.monthlyPrice : monthlyPrice // ignore: cast_nullable_to_non_nullable
as int,yearlyPrice: null == yearlyPrice ? _self.yearlyPrice : yearlyPrice // ignore: cast_nullable_to_non_nullable
as int,maxEvents: freezed == maxEvents ? _self.maxEvents : maxEvents // ignore: cast_nullable_to_non_nullable
as int?,maxProducts: freezed == maxProducts ? _self.maxProducts : maxProducts // ignore: cast_nullable_to_non_nullable
as int?,hasSetDiscountFeature: null == hasSetDiscountFeature ? _self.hasSetDiscountFeature : hasSetDiscountFeature // ignore: cast_nullable_to_non_nullable
as bool,hasServiceFeature: null == hasServiceFeature ? _self.hasServiceFeature : hasServiceFeature // ignore: cast_nullable_to_non_nullable
as bool,hasServerSyncFeature: null == hasServerSyncFeature ? _self.hasServerSyncFeature : hasServerSyncFeature // ignore: cast_nullable_to_non_nullable
as bool,hasSellerManagementFeature: null == hasSellerManagementFeature ? _self.hasSellerManagementFeature : hasSellerManagementFeature // ignore: cast_nullable_to_non_nullable
as bool,hasExcelExportFeature: null == hasExcelExportFeature ? _self.hasExcelExportFeature : hasExcelExportFeature // ignore: cast_nullable_to_non_nullable
as bool,hasPdfExportFeature: null == hasPdfExportFeature ? _self.hasPdfExportFeature : hasPdfExportFeature // ignore: cast_nullable_to_non_nullable
as bool,hasAdvancedStatsFeature: null == hasAdvancedStatsFeature ? _self.hasAdvancedStatsFeature : hasAdvancedStatsFeature // ignore: cast_nullable_to_non_nullable
as bool,hasGoogleDriveBackupFeature: null == hasGoogleDriveBackupFeature ? _self.hasGoogleDriveBackupFeature : hasGoogleDriveBackupFeature // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$UserSubscription {

/// 사용자 ID
 String get userId;/// 현재 구독 플랜
 SubscriptionPlanType get planType;/// 구독 시작일
 DateTime? get subscriptionStartDate;/// 구독 만료일 (무료 플랜의 경우 null)
 DateTime? get subscriptionEndDate;/// 구독 상태가 활성화되어 있는지
 bool get isActive;/// 구독 생성일
 DateTime get createdAt;/// 구독 수정일
 DateTime? get updatedAt;/// 🔐 보안 강화 필드들
/// 원본 구매자 사용자 ID (구독을 실제로 구매한 계정)
 String? get originalPurchaserUserId;/// Apple ID 또는 Google Account 식별자
 String? get platformAccountId;/// 플랫폼 (apple, google)
 String? get platform;/// 구매 ID (Apple: transaction ID, Google: purchase token)
 String? get purchaseId;/// 상품 ID
 String? get productId;/// 구매 소스 (in_app_purchase, admin, etc.)
 String get source;/// 🎁 관리자 보너스 기간 (관리자가 추가로 제공한 일수)
 int? get adminBonusDays;/// 🎁 관리자 보너스 만료일
 DateTime? get adminBonusEndDate;/// 구독 이전 여부 (보안상 구독 이전을 추적)
 bool get isTransferred;/// 이전 이력 (구독이 이전된 경우)
 String? get transferHistory;
/// Create a copy of UserSubscription
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UserSubscriptionCopyWith<UserSubscription> get copyWith => _$UserSubscriptionCopyWithImpl<UserSubscription>(this as UserSubscription, _$identity);

  /// Serializes this UserSubscription to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UserSubscription&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.planType, planType) || other.planType == planType)&&(identical(other.subscriptionStartDate, subscriptionStartDate) || other.subscriptionStartDate == subscriptionStartDate)&&(identical(other.subscriptionEndDate, subscriptionEndDate) || other.subscriptionEndDate == subscriptionEndDate)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.originalPurchaserUserId, originalPurchaserUserId) || other.originalPurchaserUserId == originalPurchaserUserId)&&(identical(other.platformAccountId, platformAccountId) || other.platformAccountId == platformAccountId)&&(identical(other.platform, platform) || other.platform == platform)&&(identical(other.purchaseId, purchaseId) || other.purchaseId == purchaseId)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.source, source) || other.source == source)&&(identical(other.adminBonusDays, adminBonusDays) || other.adminBonusDays == adminBonusDays)&&(identical(other.adminBonusEndDate, adminBonusEndDate) || other.adminBonusEndDate == adminBonusEndDate)&&(identical(other.isTransferred, isTransferred) || other.isTransferred == isTransferred)&&(identical(other.transferHistory, transferHistory) || other.transferHistory == transferHistory));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,planType,subscriptionStartDate,subscriptionEndDate,isActive,createdAt,updatedAt,originalPurchaserUserId,platformAccountId,platform,purchaseId,productId,source,adminBonusDays,adminBonusEndDate,isTransferred,transferHistory);

@override
String toString() {
  return 'UserSubscription(userId: $userId, planType: $planType, subscriptionStartDate: $subscriptionStartDate, subscriptionEndDate: $subscriptionEndDate, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt, originalPurchaserUserId: $originalPurchaserUserId, platformAccountId: $platformAccountId, platform: $platform, purchaseId: $purchaseId, productId: $productId, source: $source, adminBonusDays: $adminBonusDays, adminBonusEndDate: $adminBonusEndDate, isTransferred: $isTransferred, transferHistory: $transferHistory)';
}


}

/// @nodoc
abstract mixin class $UserSubscriptionCopyWith<$Res>  {
  factory $UserSubscriptionCopyWith(UserSubscription value, $Res Function(UserSubscription) _then) = _$UserSubscriptionCopyWithImpl;
@useResult
$Res call({
 String userId, SubscriptionPlanType planType, DateTime? subscriptionStartDate, DateTime? subscriptionEndDate, bool isActive, DateTime createdAt, DateTime? updatedAt, String? originalPurchaserUserId, String? platformAccountId, String? platform, String? purchaseId, String? productId, String source, int? adminBonusDays, DateTime? adminBonusEndDate, bool isTransferred, String? transferHistory
});




}
/// @nodoc
class _$UserSubscriptionCopyWithImpl<$Res>
    implements $UserSubscriptionCopyWith<$Res> {
  _$UserSubscriptionCopyWithImpl(this._self, this._then);

  final UserSubscription _self;
  final $Res Function(UserSubscription) _then;

/// Create a copy of UserSubscription
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? userId = null,Object? planType = null,Object? subscriptionStartDate = freezed,Object? subscriptionEndDate = freezed,Object? isActive = null,Object? createdAt = null,Object? updatedAt = freezed,Object? originalPurchaserUserId = freezed,Object? platformAccountId = freezed,Object? platform = freezed,Object? purchaseId = freezed,Object? productId = freezed,Object? source = null,Object? adminBonusDays = freezed,Object? adminBonusEndDate = freezed,Object? isTransferred = null,Object? transferHistory = freezed,}) {
  return _then(_self.copyWith(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,planType: null == planType ? _self.planType : planType // ignore: cast_nullable_to_non_nullable
as SubscriptionPlanType,subscriptionStartDate: freezed == subscriptionStartDate ? _self.subscriptionStartDate : subscriptionStartDate // ignore: cast_nullable_to_non_nullable
as DateTime?,subscriptionEndDate: freezed == subscriptionEndDate ? _self.subscriptionEndDate : subscriptionEndDate // ignore: cast_nullable_to_non_nullable
as DateTime?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,originalPurchaserUserId: freezed == originalPurchaserUserId ? _self.originalPurchaserUserId : originalPurchaserUserId // ignore: cast_nullable_to_non_nullable
as String?,platformAccountId: freezed == platformAccountId ? _self.platformAccountId : platformAccountId // ignore: cast_nullable_to_non_nullable
as String?,platform: freezed == platform ? _self.platform : platform // ignore: cast_nullable_to_non_nullable
as String?,purchaseId: freezed == purchaseId ? _self.purchaseId : purchaseId // ignore: cast_nullable_to_non_nullable
as String?,productId: freezed == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String?,source: null == source ? _self.source : source // ignore: cast_nullable_to_non_nullable
as String,adminBonusDays: freezed == adminBonusDays ? _self.adminBonusDays : adminBonusDays // ignore: cast_nullable_to_non_nullable
as int?,adminBonusEndDate: freezed == adminBonusEndDate ? _self.adminBonusEndDate : adminBonusEndDate // ignore: cast_nullable_to_non_nullable
as DateTime?,isTransferred: null == isTransferred ? _self.isTransferred : isTransferred // ignore: cast_nullable_to_non_nullable
as bool,transferHistory: freezed == transferHistory ? _self.transferHistory : transferHistory // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [UserSubscription].
extension UserSubscriptionPatterns on UserSubscription {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _UserSubscription value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _UserSubscription() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _UserSubscription value)  $default,){
final _that = this;
switch (_that) {
case _UserSubscription():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _UserSubscription value)?  $default,){
final _that = this;
switch (_that) {
case _UserSubscription() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String userId,  SubscriptionPlanType planType,  DateTime? subscriptionStartDate,  DateTime? subscriptionEndDate,  bool isActive,  DateTime createdAt,  DateTime? updatedAt,  String? originalPurchaserUserId,  String? platformAccountId,  String? platform,  String? purchaseId,  String? productId,  String source,  int? adminBonusDays,  DateTime? adminBonusEndDate,  bool isTransferred,  String? transferHistory)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _UserSubscription() when $default != null:
return $default(_that.userId,_that.planType,_that.subscriptionStartDate,_that.subscriptionEndDate,_that.isActive,_that.createdAt,_that.updatedAt,_that.originalPurchaserUserId,_that.platformAccountId,_that.platform,_that.purchaseId,_that.productId,_that.source,_that.adminBonusDays,_that.adminBonusEndDate,_that.isTransferred,_that.transferHistory);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String userId,  SubscriptionPlanType planType,  DateTime? subscriptionStartDate,  DateTime? subscriptionEndDate,  bool isActive,  DateTime createdAt,  DateTime? updatedAt,  String? originalPurchaserUserId,  String? platformAccountId,  String? platform,  String? purchaseId,  String? productId,  String source,  int? adminBonusDays,  DateTime? adminBonusEndDate,  bool isTransferred,  String? transferHistory)  $default,) {final _that = this;
switch (_that) {
case _UserSubscription():
return $default(_that.userId,_that.planType,_that.subscriptionStartDate,_that.subscriptionEndDate,_that.isActive,_that.createdAt,_that.updatedAt,_that.originalPurchaserUserId,_that.platformAccountId,_that.platform,_that.purchaseId,_that.productId,_that.source,_that.adminBonusDays,_that.adminBonusEndDate,_that.isTransferred,_that.transferHistory);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String userId,  SubscriptionPlanType planType,  DateTime? subscriptionStartDate,  DateTime? subscriptionEndDate,  bool isActive,  DateTime createdAt,  DateTime? updatedAt,  String? originalPurchaserUserId,  String? platformAccountId,  String? platform,  String? purchaseId,  String? productId,  String source,  int? adminBonusDays,  DateTime? adminBonusEndDate,  bool isTransferred,  String? transferHistory)?  $default,) {final _that = this;
switch (_that) {
case _UserSubscription() when $default != null:
return $default(_that.userId,_that.planType,_that.subscriptionStartDate,_that.subscriptionEndDate,_that.isActive,_that.createdAt,_that.updatedAt,_that.originalPurchaserUserId,_that.platformAccountId,_that.platform,_that.purchaseId,_that.productId,_that.source,_that.adminBonusDays,_that.adminBonusEndDate,_that.isTransferred,_that.transferHistory);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _UserSubscription implements UserSubscription {
  const _UserSubscription({required this.userId, required this.planType, this.subscriptionStartDate, this.subscriptionEndDate, this.isActive = true, required this.createdAt, this.updatedAt, this.originalPurchaserUserId, this.platformAccountId, this.platform, this.purchaseId, this.productId, this.source = 'in_app_purchase', this.adminBonusDays, this.adminBonusEndDate, this.isTransferred = false, this.transferHistory});
  factory _UserSubscription.fromJson(Map<String, dynamic> json) => _$UserSubscriptionFromJson(json);

/// 사용자 ID
@override final  String userId;
/// 현재 구독 플랜
@override final  SubscriptionPlanType planType;
/// 구독 시작일
@override final  DateTime? subscriptionStartDate;
/// 구독 만료일 (무료 플랜의 경우 null)
@override final  DateTime? subscriptionEndDate;
/// 구독 상태가 활성화되어 있는지
@override@JsonKey() final  bool isActive;
/// 구독 생성일
@override final  DateTime createdAt;
/// 구독 수정일
@override final  DateTime? updatedAt;
/// 🔐 보안 강화 필드들
/// 원본 구매자 사용자 ID (구독을 실제로 구매한 계정)
@override final  String? originalPurchaserUserId;
/// Apple ID 또는 Google Account 식별자
@override final  String? platformAccountId;
/// 플랫폼 (apple, google)
@override final  String? platform;
/// 구매 ID (Apple: transaction ID, Google: purchase token)
@override final  String? purchaseId;
/// 상품 ID
@override final  String? productId;
/// 구매 소스 (in_app_purchase, admin, etc.)
@override@JsonKey() final  String source;
/// 🎁 관리자 보너스 기간 (관리자가 추가로 제공한 일수)
@override final  int? adminBonusDays;
/// 🎁 관리자 보너스 만료일
@override final  DateTime? adminBonusEndDate;
/// 구독 이전 여부 (보안상 구독 이전을 추적)
@override@JsonKey() final  bool isTransferred;
/// 이전 이력 (구독이 이전된 경우)
@override final  String? transferHistory;

/// Create a copy of UserSubscription
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UserSubscriptionCopyWith<_UserSubscription> get copyWith => __$UserSubscriptionCopyWithImpl<_UserSubscription>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UserSubscriptionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UserSubscription&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.planType, planType) || other.planType == planType)&&(identical(other.subscriptionStartDate, subscriptionStartDate) || other.subscriptionStartDate == subscriptionStartDate)&&(identical(other.subscriptionEndDate, subscriptionEndDate) || other.subscriptionEndDate == subscriptionEndDate)&&(identical(other.isActive, isActive) || other.isActive == isActive)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt)&&(identical(other.originalPurchaserUserId, originalPurchaserUserId) || other.originalPurchaserUserId == originalPurchaserUserId)&&(identical(other.platformAccountId, platformAccountId) || other.platformAccountId == platformAccountId)&&(identical(other.platform, platform) || other.platform == platform)&&(identical(other.purchaseId, purchaseId) || other.purchaseId == purchaseId)&&(identical(other.productId, productId) || other.productId == productId)&&(identical(other.source, source) || other.source == source)&&(identical(other.adminBonusDays, adminBonusDays) || other.adminBonusDays == adminBonusDays)&&(identical(other.adminBonusEndDate, adminBonusEndDate) || other.adminBonusEndDate == adminBonusEndDate)&&(identical(other.isTransferred, isTransferred) || other.isTransferred == isTransferred)&&(identical(other.transferHistory, transferHistory) || other.transferHistory == transferHistory));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,planType,subscriptionStartDate,subscriptionEndDate,isActive,createdAt,updatedAt,originalPurchaserUserId,platformAccountId,platform,purchaseId,productId,source,adminBonusDays,adminBonusEndDate,isTransferred,transferHistory);

@override
String toString() {
  return 'UserSubscription(userId: $userId, planType: $planType, subscriptionStartDate: $subscriptionStartDate, subscriptionEndDate: $subscriptionEndDate, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt, originalPurchaserUserId: $originalPurchaserUserId, platformAccountId: $platformAccountId, platform: $platform, purchaseId: $purchaseId, productId: $productId, source: $source, adminBonusDays: $adminBonusDays, adminBonusEndDate: $adminBonusEndDate, isTransferred: $isTransferred, transferHistory: $transferHistory)';
}


}

/// @nodoc
abstract mixin class _$UserSubscriptionCopyWith<$Res> implements $UserSubscriptionCopyWith<$Res> {
  factory _$UserSubscriptionCopyWith(_UserSubscription value, $Res Function(_UserSubscription) _then) = __$UserSubscriptionCopyWithImpl;
@override @useResult
$Res call({
 String userId, SubscriptionPlanType planType, DateTime? subscriptionStartDate, DateTime? subscriptionEndDate, bool isActive, DateTime createdAt, DateTime? updatedAt, String? originalPurchaserUserId, String? platformAccountId, String? platform, String? purchaseId, String? productId, String source, int? adminBonusDays, DateTime? adminBonusEndDate, bool isTransferred, String? transferHistory
});




}
/// @nodoc
class __$UserSubscriptionCopyWithImpl<$Res>
    implements _$UserSubscriptionCopyWith<$Res> {
  __$UserSubscriptionCopyWithImpl(this._self, this._then);

  final _UserSubscription _self;
  final $Res Function(_UserSubscription) _then;

/// Create a copy of UserSubscription
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? userId = null,Object? planType = null,Object? subscriptionStartDate = freezed,Object? subscriptionEndDate = freezed,Object? isActive = null,Object? createdAt = null,Object? updatedAt = freezed,Object? originalPurchaserUserId = freezed,Object? platformAccountId = freezed,Object? platform = freezed,Object? purchaseId = freezed,Object? productId = freezed,Object? source = null,Object? adminBonusDays = freezed,Object? adminBonusEndDate = freezed,Object? isTransferred = null,Object? transferHistory = freezed,}) {
  return _then(_UserSubscription(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,planType: null == planType ? _self.planType : planType // ignore: cast_nullable_to_non_nullable
as SubscriptionPlanType,subscriptionStartDate: freezed == subscriptionStartDate ? _self.subscriptionStartDate : subscriptionStartDate // ignore: cast_nullable_to_non_nullable
as DateTime?,subscriptionEndDate: freezed == subscriptionEndDate ? _self.subscriptionEndDate : subscriptionEndDate // ignore: cast_nullable_to_non_nullable
as DateTime?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: freezed == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,originalPurchaserUserId: freezed == originalPurchaserUserId ? _self.originalPurchaserUserId : originalPurchaserUserId // ignore: cast_nullable_to_non_nullable
as String?,platformAccountId: freezed == platformAccountId ? _self.platformAccountId : platformAccountId // ignore: cast_nullable_to_non_nullable
as String?,platform: freezed == platform ? _self.platform : platform // ignore: cast_nullable_to_non_nullable
as String?,purchaseId: freezed == purchaseId ? _self.purchaseId : purchaseId // ignore: cast_nullable_to_non_nullable
as String?,productId: freezed == productId ? _self.productId : productId // ignore: cast_nullable_to_non_nullable
as String?,source: null == source ? _self.source : source // ignore: cast_nullable_to_non_nullable
as String,adminBonusDays: freezed == adminBonusDays ? _self.adminBonusDays : adminBonusDays // ignore: cast_nullable_to_non_nullable
as int?,adminBonusEndDate: freezed == adminBonusEndDate ? _self.adminBonusEndDate : adminBonusEndDate // ignore: cast_nullable_to_non_nullable
as DateTime?,isTransferred: null == isTransferred ? _self.isTransferred : isTransferred // ignore: cast_nullable_to_non_nullable
as bool,transferHistory: freezed == transferHistory ? _self.transferHistory : transferHistory // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
