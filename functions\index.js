/**
 * Firebase Functions for User Data Management (2nd Generation)
 *
 * 사용자 계정 삭제 시 모든 관련 데이터를 안전하게 정리하는 Functions
 */

const { onCall, onRequest } = require('firebase-functions/v2/https');
const admin = require("firebase-admin");
const axios = require('axios');

// Firebase Admin SDK 초기화 (이미 초기화되어 있으면 중복 방지)
if (!admin.apps.length) {
  const serviceAccount = require("./parabara-1a504-288e9c6e5d05.json");
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    storageBucket: "parabara-1a504.firebasestorage.app"
  });
}

// reCAPTCHA 설정 제거됨 - Firebase App Check 사용



// reCAPTCHA 관련 함수 제거됨 - Firebase App Check 사용

/**
 * 사용자 데이터 완전 삭제 함수 (2세대 onCall)
 * 클라이언트에서 호출하여 모든 데이터를 안전하게 삭제합니다.
 */
exports.deleteUserData = onCall(async (request) => {
  // 인증 확인
  if (!request.auth) {
    throw new Error('인증이 필요합니다.');
  }

  const uid = request.auth.uid;
  const userEmail = request.auth.token.email || 'unknown';

  console.log(`🗑️ 사용자 데이터 삭제 시작: ${userEmail} (${uid})`);

  try {
    // 1. 사용자 전화번호 정보 미리 조회 (SMS 인증 데이터 삭제용)
    console.log(`📞 사용자 전화번호 정보 조회: ${uid}`);
    let userPhoneNumber = null;
    try {
      const userDoc = await admin.firestore().collection('users').doc(uid).get();
      if (userDoc.exists && userDoc.data().phone) {
        userPhoneNumber = userDoc.data().phone;
        console.log(`📞 사용자 전화번호 발견: ${userPhoneNumber}`);
      }
    } catch (e) {
      console.log(`📞 사용자 전화번호 조회 실패 (계속 진행): ${e.message}`);
    }

    // 2. SMS 인증 데이터 삭제 (전화번호 기반)
    if (userPhoneNumber) {
      console.log(`📱 SMS 인증 데이터 삭제 시작: ${userPhoneNumber}`);
      try {
        // sms_verifications 컬렉션에서 해당 전화번호 문서 삭제
        await admin.firestore().collection('sms_verifications').doc(userPhoneNumber).delete();
        console.log(`✅ SMS 인증 데이터 삭제 완료: ${userPhoneNumber}`);
      } catch (e) {
        console.log(`⚠️ SMS 인증 데이터 삭제 실패 (계속 진행): ${e.message}`);
      }

      // phone_numbers 컬렉션에서도 삭제
      try {
        await admin.firestore().collection('phone_numbers').doc(userPhoneNumber).delete();
        console.log(`✅ 전화번호 소유권 데이터 삭제 완료: ${userPhoneNumber}`);
      } catch (e) {
        console.log(`⚠️ 전화번호 소유권 데이터 삭제 실패 (계속 진행): ${e.message}`);
      }
    }

    // 3. UID 기반 SMS 인증 데이터 추가 삭제 (전화번호를 못 찾은 경우 대비)
    console.log(`📱 UID 기반 SMS 인증 데이터 검색 및 삭제: ${uid}`);
    try {
      const smsQuery = await admin.firestore()
        .collection('sms_verifications')
        .where('uid', '==', uid)
        .get();

      if (!smsQuery.empty) {
        const batch = admin.firestore().batch();
        smsQuery.docs.forEach(doc => {
          batch.delete(doc.ref);
        });
        await batch.commit();
        console.log(`✅ UID 기반 SMS 인증 데이터 삭제 완료: ${smsQuery.size}개 문서`);
      } else {
        console.log(`📱 UID 기반 SMS 인증 데이터 없음`);
      }
    } catch (e) {
      console.log(`⚠️ UID 기반 SMS 인증 데이터 삭제 실패 (계속 진행): ${e.message}`);
    }

    // 4. Firestore 데이터 재귀 삭제 (사용자 문서 + 모든 하위 컬렉션)
    console.log(`📄 Firestore 재귀 삭제 시작: ${uid}`);
    const userRef = admin.firestore().collection('users').doc(uid);
    await admin.firestore().recursiveDelete(userRef);
    console.log(`✅ Firestore 모든 데이터 삭제 완료: ${uid}`);

    // 5. Storage 파일 전체 삭제 (사용자 폴더 통째로)
    console.log(`🗂️ Storage 폴더 삭제 시작: users/${uid}/`);
    const bucket = admin.storage().bucket();
    const [files] = await bucket.getFiles({ prefix: `users/${uid}/` });

    if (files.length > 0) {
      await Promise.all(files.map(file => file.delete()));
      console.log(`✅ Storage 파일 삭제 완료: ${files.length}개 파일`);
    } else {
      console.log(`📁 Storage에 삭제할 파일 없음`);
    }

    // 6. Authentication 계정 삭제 (마지막에)
    console.log(`🔐 Auth 계정 삭제 시작: ${uid}`);
    await admin.auth().deleteUser(uid);
    console.log(`✅ Auth 계정 삭제 완료: ${uid}`);

    console.log(`🎉 사용자 데이터 완전 삭제 완료: ${userEmail} (${uid})`);

    return {
      success: true,
      message: '모든 사용자 데이터가 성공적으로 삭제되었습니다.',
      deletedUid: uid
    };

  } catch (error) {
    console.error(`❌ 사용자 데이터 삭제 실패: ${userEmail} (${uid})`, error);
    throw new Error(`데이터 삭제 중 오류가 발생했습니다: ${error.message}`);
  }
});

// ❌ createBillingKey 함수 제거됨 - 클라이언트에서 직접 처리

// ❌ processBillingPayment 함수 제거됨 - 클라이언트에서 직접 처리

/**
 * 🔄 정기 결제 시스템 (비활성화됨)
 * 나이스페이 결제 시스템이 제거되고 인앱 결제로 변경되었습니다.
 * 이 함수는 호환성을 위해 유지되지만 실제 결제 처리는 하지 않습니다.
 */
exports.processDailySubscriptions = onRequest({
  cors: true,
  invoker: 'public'
}, async (req, res) => {
  console.log('🔄 정기 결제 시스템 호출됨 (나이스페이 제거로 인해 비활성화)');

  try {
    const db = admin.firestore();
    const now = new Date();
    const kstTime = new Date(now.getTime() + (9 * 60 * 60 * 1000));
    const logTime = kstTime.toISOString().replace('T', ' ').substring(0, 19);
    const docName = `자동결제로그 : ${logTime} KST`;

    // 나이스페이 제거 로그 저장
    await db.collection('logs').doc(docName).set({
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      type: 'daily_subscription_check',
      totalProcessed: 0,
      successCount: 0,
      failureCount: 0,
      message: '나이스페이 결제 시스템 제거됨 - 인앱 결제로 변경',
      level: 'INFO',
      details: {
        reason: 'nicepay_removed',
        newSystem: 'in_app_purchase'
      },
      executedBy: 'Cloud Scheduler'
    });

    console.log('✅ 나이스페이 제거 로그 저장 완료');

    return res.status(200).json({
      success: true,
      message: '나이스페이 결제 시스템이 제거되어 정기 결제가 비활성화되었습니다.',
      processedCount: 0,
      results: [],
      note: '인앱 결제 시스템으로 변경되었습니다.'
    });

  } catch (error) {
    console.error('❌ 정기 결제 시스템 오류:', error);

    return res.status(200).json({
      success: false,
      message: '나이스페이 결제 시스템이 제거되어 정기 결제가 비활성화되었습니다.',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});





/**
 * 🔐 관리자 인증 함수
 */
exports.adminAuth = onRequest({ cors: true }, async (req, res) => {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { username, password } = req.body;

  // 🔐 관리자 계정 확인 (환경변수 사용)
  const ADMIN_USERNAME = process.env.ADMIN_USERNAME;
  const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD;

  if (!ADMIN_USERNAME || !ADMIN_PASSWORD) {
    return res.status(500).json({
      success: false,
      message: '관리자 계정 설정 오류'
    });
  }

  if (username === ADMIN_USERNAME && password === ADMIN_PASSWORD) {
    // JWT 토큰 생성
    const jwt = require('jsonwebtoken');

    // JWT 시크릿 키
    const JWT_SECRET = process.env.JWT_SECRET || 'parabara-admin-secret-key-2024';

    // JWT 페이로드
    const payload = {
      admin: true,
      username: username,
      loginTime: Date.now(),
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24시간 만료
    };

    const token = jwt.sign(payload, JWT_SECRET);

    // 접근 로그 기록
    const loginLog = {
      timestamp: new Date().toISOString(),
      username: username,
      ip: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent'),
      success: true
    };

    // 비동기로 로그 저장
    setImmediate(async () => {
      try {
        const now = new Date();
        const kstTime = new Date(now.getTime() + (9 * 60 * 60 * 1000));
        const logTime = kstTime.toISOString().replace('T', ' ').substring(0, 19);
        const docName = `관리자로그 : ${logTime} KST`;

        await admin.firestore().collection('admin_logs').doc(docName).set(loginLog);
      } catch (error) {
        console.error('관리자 로그 저장 오류:', error);
      }
    });

    console.log('✅ 관리자 로그인 성공:', loginLog);

    res.json({
      success: true,
      token: token,
      message: '관리자 인증 성공',
      expiresIn: '24시간'
    });
  } else {
    // 실패 로그 기록
    const failLog = {
      timestamp: new Date().toISOString(),
      username: username,
      ip: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent'),
      success: false,
      reason: '잘못된 인증정보'
    };

    setImmediate(async () => {
      try {
        const now = new Date();
        const kstTime = new Date(now.getTime() + (9 * 60 * 60 * 1000));
        const logTime = kstTime.toISOString().replace('T', ' ').substring(0, 19);
        const docName = `관리자로그 : ${logTime} KST`;

        await admin.firestore().collection('admin_logs').doc(docName).set(failLog);
      } catch (error) {
        console.error('관리자 로그 저장 오류:', error);
      }
    });

    console.log('❌ 관리자 로그인 실패:', failLog);

    res.status(401).json({
      success: false,
      message: '인증 실패'
    });
  }
});

/**
 * 📊 구독 통계 전용 컬렉션 초기화
 * subscription_stats 컬렉션을 생성하고 초기 데이터를 설정합니다.
 */
async function initializeSubscriptionStats(db) {
  try {
    console.log('📊 subscription_stats 컬렉션 초기화 시작');

    const now = new Date();

    // 활성 구독자 수 계산
    const activeSubscriptions = await db.collectionGroup('subscriptions')
      .where('status', '==', 'active')
      .get();
    const activeSubscribers = activeSubscriptions.size;

    // 월별 매출 통계 계산 (최근 6개월)
    const monthlyRevenue = [];
    for (let i = 5; i >= 0; i--) {
      const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0);

      const monthlySubscriptions = await db.collectionGroup('subscriptions')
        .where('status', '==', 'active')
        .where('lastPaymentDate', '>=', admin.firestore.Timestamp.fromDate(monthStart))
        .where('lastPaymentDate', '<=', admin.firestore.Timestamp.fromDate(monthEnd))
        .get();

      monthlyRevenue.push({
        month: monthStart.toISOString().substring(0, 7), // YYYY-MM 형식
        revenue: monthlySubscriptions.size * 100, // 월 100원 * 구독자 수
        subscribers: monthlySubscriptions.size
      });
    }

    // subscription_stats 컬렉션에 저장
    await db.collection('subscription_stats').doc('current').set({
      activeSubscribers,
      monthlyRevenue,
      lastUpdated: admin.firestore.FieldValue.serverTimestamp(),
      initializedAt: now.toISOString()
    });

    console.log(`📊 subscription_stats 초기화 완료 - 활성 구독자: ${activeSubscribers}명`);

  } catch (error) {
    console.error('subscription_stats 초기화 오류:', error);
  }
}

/**
 * 📊 구독 통계 업데이트
 * 구독 상태 변경 시 subscription_stats를 업데이트합니다.
 */
exports.updateSubscriptionStats = onRequest({
  cors: true,
  invoker: 'public'
}, async (req, res) => {
  try {
    const db = admin.firestore();
    await initializeSubscriptionStats(db);

    res.json({
      success: true,
      message: 'subscription_stats 업데이트 완료'
    });

  } catch (error) {
    console.error('subscription_stats 업데이트 오류:', error);
    res.status(500).json({
      success: false,
      error: 'subscription_stats 업데이트 실패'
    });
  }
});



/**
 * 🔐 JWT 토큰 검증 미들웨어
 */
function verifyAdminToken(req, res, next) {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      success: false,
      error: '인증 토큰이 필요합니다.'
    });
  }

  const token = authHeader.split('Bearer ')[1];
  const jwt = require('jsonwebtoken');
  const JWT_SECRET = process.env.JWT_SECRET || 'parabara-admin-secret-key-2024';

  try {
    const decoded = jwt.verify(token, JWT_SECRET);

    if (!decoded.admin) {
      return res.status(403).json({
        success: false,
        error: '관리자 권한이 필요합니다.'
      });
    }

    req.admin = decoded;
    next();
  } catch (error) {
    console.error('JWT 토큰 검증 실패:', error.message);
    return res.status(401).json({
      success: false,
      error: '유효하지 않은 토큰입니다.'
    });
  }
}

/**
 * 📊 관리자 대시보드 데이터 조회 (온디맨드 + 스마트 캐싱)
 * 관리자가 대시보드에 접속할 때만 통계를 생성하고, 캐시를 활용하여 성능 최적화
 */
exports.adminDashboard = onRequest({ cors: true }, async (req, res) => {
  // 간단한 토큰 검증 (실제 운영에서는 더 강화된 검증 필요)
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: '인증 토큰이 필요합니다' });
  }

  try {
    const db = admin.firestore();
    const now = new Date();

    // 캐시 확인 (24시간 캐시로 연장하여 읽기 최적화)
    const cacheKey = 'admin_dashboard_stats';
    const cacheExpiry = 24 * 60 * 60 * 1000; // 24시간

    try {
      const cachedDoc = await db.collection('admin_cache').doc(cacheKey).get();
      if (cachedDoc.exists) {
        const cachedData = cachedDoc.data();
        const cacheAge = now.getTime() - cachedData.timestamp.toDate().getTime();

        if (cacheAge < cacheExpiry) {
          console.log(`📊 캐시된 대시보드 데이터 반환 (${Math.floor(cacheAge / 60000)}분 전 생성)`);
          return res.json({
            success: true,
            data: cachedData.stats,
            cached: true,
            cacheAge: Math.floor(cacheAge / 60000) + '분 전'
          });
        }
      }
    } catch (cacheError) {
      console.log('캐시 조회 실패, 실시간 생성으로 진행:', cacheError.message);
    }

    console.log('📊 실시간 대시보드 통계 생성 시작');
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    // 🔥 최적화: 전체 사용자 수를 카운터 문서에서 조회 (1개 읽기)
    let totalUsers = 0;
    let recentUsers = 0;
    let weeklyUsers = 0;

    try {
      const userStatsDoc = await db.collection('user_stats').doc('current').get();
      if (userStatsDoc.exists) {
        const statsData = userStatsDoc.data();
        totalUsers = statsData.totalUsers || 0;
        recentUsers = statsData.recentUsers || 0;
        weeklyUsers = statsData.weeklyUsers || 0;
        console.log('📊 사용자 통계 전용 컬렉션에서 데이터 조회 완료');
      } else {
        // 폴백: 실시간 조회 (성능 저하)
        console.log('⚠️ 사용자 통계 문서가 없어 실시간 조회');
        const usersSnapshot = await db.collection('users').get();
        totalUsers = usersSnapshot.size;

        const recentUsersSnapshot = await db.collection('users')
          .where('createdAt', '>=', admin.firestore.Timestamp.fromDate(thirtyDaysAgo))
          .get();
        recentUsers = recentUsersSnapshot.size;

        const weeklyUsersSnapshot = await db.collection('users')
          .where('createdAt', '>=', admin.firestore.Timestamp.fromDate(sevenDaysAgo))
          .get();
        weeklyUsers = weeklyUsersSnapshot.size;
      }
    } catch (error) {
      console.error('사용자 통계 조회 실패:', error);
      totalUsers = 0;
      recentUsers = 0;
      weeklyUsers = 0;
    }

    // 활성 구독자 수 (subscription_stats 컬렉션 사용)
    let activeSubscribers = 0;
    let monthlyRevenue = [];
    try {
      const subscriptionStatsDoc = await db.collection('subscription_stats').doc('current').get();
      if (subscriptionStatsDoc.exists) {
        const statsData = subscriptionStatsDoc.data();
        activeSubscribers = statsData.activeSubscribers || 0;
        monthlyRevenue = statsData.monthlyRevenue || [];
        console.log('📊 구독 통계 전용 컬렉션에서 데이터 조회 완료');
      } else {
        // 백업: 실시간 조회 (subscription_stats가 없는 경우)
        console.log('⚠️ subscription_stats 없음, 실시간 조회로 대체');
        const activeSubscriptions = await db.collectionGroup('subscriptions')
          .where('status', '==', 'active')
          .get();
        activeSubscribers = activeSubscriptions.size;

        // subscription_stats 초기화
        await initializeSubscriptionStats(db);
      }
    } catch (error) {
      console.error('구독 통계 조회 오류:', error);
      activeSubscribers = 0;
      monthlyRevenue = [];
    }

    // 무료 플랜 사용자 수
    const freeUsers = totalUsers - activeSubscribers;

    // 최근 결제 내역 (최적화: 사용자 정보 개별 조회 제거)
    const recentPayments = await db.collectionGroup('subscriptions')
      .where('status', '==', 'active')
      .orderBy('lastPaymentDate', 'desc')
      .limit(5) // 10개에서 5개로 줄여서 읽기 최적화
      .get();

    const payments = recentPayments.docs.map(doc => {
      const subscriptionData = doc.data();
      const userId = doc.ref.parent.parent.id;

      return {
        id: doc.id,
        userId: userId,
        userEmail: 'N/A', // 개별 조회 제거로 성능 최적화
        userNickname: 'N/A', // 개별 조회 제거로 성능 최적화
        amount: 100, // 월 100원 고정
        plan: subscriptionData.plan || 'plus',
        status: subscriptionData.status,
        lastPaymentDate: subscriptionData.lastPaymentDate?.toDate?.()?.toISOString() || null,
        nextPaymentDate: subscriptionData.nextPaymentDate?.toDate?.()?.toISOString() || null,
        paymentMethod: 'card'
      };
    });

    // 월별 매출 통계는 subscription_stats에서 이미 가져옴 (위에서 처리됨)

    // 서버 사용량 통계 (추정치)
    const serverStats = {
      totalFunctionCalls: Math.floor(Math.random() * 10000) + 5000, // 실제로는 Cloud Monitoring에서 가져와야 함
      totalStorageUsed: Math.floor(Math.random() * 1000) + 500, // MB 단위
      totalBandwidthUsed: Math.floor(Math.random() * 5000) + 2000, // MB 단위
      averageResponseTime: Math.floor(Math.random() * 500) + 100, // ms 단위
      errorRate: (Math.random() * 2).toFixed(2) + '%'
    };

    // 사용자 활동 통계
    const userActivityStats = {
      dailyActiveUsers: Math.floor(totalUsers * 0.3), // 추정치
      weeklyActiveUsers: Math.floor(totalUsers * 0.6), // 추정치
      monthlyActiveUsers: Math.floor(totalUsers * 0.8), // 추정치
      averageSessionDuration: Math.floor(Math.random() * 30) + 15 + ' minutes'
    };

    res.json({
      success: true,
      data: {
        // 기본 통계
        totalUsers,
        activeSubscribers,
        freeUsers,
        recentUsers,
        weeklyUsers,

        // 매출 통계
        monthlyRevenue,
        totalRevenue: activeSubscribers * 100, // 현재 월 매출

        // 결제 내역
        recentPayments: payments,

        // 서버 사용량
        serverStats,

        // 사용자 활동
        userActivityStats,

        // 성장률 계산
        growthRate: {
          weekly: weeklyUsers > 0 ? ((weeklyUsers / Math.max(totalUsers - weeklyUsers, 1)) * 100).toFixed(1) + '%' : '0%',
          monthly: recentUsers > 0 ? ((recentUsers / Math.max(totalUsers - recentUsers, 1)) * 100).toFixed(1) + '%' : '0%'
        },

        timestamp: now.toISOString()
      }
    });

    // 캐시에 저장 (백그라운드에서 비동기 처리)
    const statsData = responseData.data;
    setImmediate(async () => {
      try {
        await db.collection('admin_cache').doc(cacheKey).set({
          stats: statsData,
          timestamp: admin.firestore.FieldValue.serverTimestamp(),
          generatedAt: now.toISOString()
        });
        console.log('📊 대시보드 통계 캐시 저장 완료');
      } catch (cacheError) {
        console.error('캐시 저장 오류:', cacheError);
      }
    });

    res.json(responseData);

  } catch (error) {
    console.error('관리자 대시보드 오류:', error);
    res.status(500).json({
      success: false,
      error: '데이터 조회 중 오류가 발생했습니다'
    });
  }
});

/**
 * 👥 사용자 목록 조회 (고급 검색/정렬/필터링 지원)
 */
exports.adminUsers = onRequest({ cors: true }, async (req, res) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: '인증 토큰이 필요합니다' });
  }

  try {
    const db = admin.firestore();

    // 쿼리 파라미터 파싱
    const {
      page = 1,
      limit = 50,
      searchQuery = '',
      sortBy = 'createdAt',
      sortOrder = 'desc',
      statusFilter = 'all'
    } = req.query;

    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const offset = (pageNum - 1) * limitNum;

    // 기본 사용자 쿼리
    let usersQuery = db.collection('users');

    // 정렬 적용
    if (sortBy === 'createdAt' || sortBy === 'updatedAt') {
      usersQuery = usersQuery.orderBy(sortBy, sortOrder);
    } else if (sortBy === 'email') {
      usersQuery = usersQuery.orderBy('email', sortOrder);
    }

    // 페이지네이션 적용
    if (offset > 0) {
      const offsetSnapshot = await usersQuery.limit(offset).get();
      if (!offsetSnapshot.empty) {
        const lastDoc = offsetSnapshot.docs[offsetSnapshot.docs.length - 1];
        usersQuery = usersQuery.startAfter(lastDoc);
      }
    }

    const usersSnapshot = await usersQuery.limit(limitNum).get();

    // 🔥 최적화: 더 효율적인 배치 처리
    const users = [];
    const userIds = usersSnapshot.docs.map(doc => doc.id);

    // 최대 20개까지만 처리하여 읽기 제한
    const limitedUserIds = userIds.slice(0, 20);

    // 모든 구독 정보를 한 번에 조회 (병렬 처리)
    const subscriptionPromises = limitedUserIds.map(userId =>
      db.collection('users').doc(userId).collection('subscriptions').doc('current').get()
        .catch(error => {
          console.warn(`구독 정보 조회 실패 (${userId}):`, error.message);
          return null; // 실패 시 null 반환
        })
    );
    const subscriptionDocs = await Promise.all(subscriptionPromises);

    // 결과 조합 (제한된 수만 처리)
    for (let i = 0; i < Math.min(limitedUserIds.length, usersSnapshot.docs.length); i++) {
      const userDoc = usersSnapshot.docs[i];
      const userData = userDoc.data();
      const subscriptionDoc = subscriptionDocs[i];

      const subscriptionData = subscriptionDoc?.exists ? subscriptionDoc.data() : null;

      // 사용자 정보 구성
      const userInfo = {
        uid: userDoc.id,
        email: userData.email || 'N/A',
        nickname: userData.nickname || 'N/A', // displayName 대신 nickname 사용
        phone: userData.phone || 'N/A',
        profileImageUrl: userData.profileImageUrl || null,
        createdAt: userData.createdAt?.toDate?.()?.toISOString() || null,
        updatedAt: userData.updatedAt?.toDate?.()?.toISOString() || null,
        lastLoginAt: userData.lastLoginAt?.toDate?.()?.toISOString() || null,

        // 구독 정보 (subscriptions/current에서만 가져옴)
        subscription: subscriptionData ? {
          status: subscriptionData.status,
          plan: subscriptionData.plan,
          planType: subscriptionData.planType, // planType 필드 추가
          nextPaymentDate: subscriptionData.status === 'active' ?
            subscriptionData.nextPaymentDate?.toDate?.()?.toISOString() : null,
          lastPaymentDate: subscriptionData.lastPaymentDate?.toDate?.()?.toISOString() || null,
          createdAt: subscriptionData.createdAt?.toDate?.()?.toISOString() || null
        } : {
          status: 'free',
          plan: 'free',
          planType: 'SubscriptionPlanType.free', // 기본값
          nextPaymentDate: null,
          lastPaymentDate: null,
          createdAt: null
        },

        // 약관 동의 정보
        agreement: userData.agreement || null
      };

      // subscriptionStatus 필드 추가 (웹 페이지 호환성)
      if (subscriptionData?.planType === 'SubscriptionPlanType.plus') {
        userInfo.subscriptionStatus = '플러스 플랜';
      } else if (subscriptionData?.planType === 'SubscriptionPlanType.free') {
        userInfo.subscriptionStatus = '무료 플랜';
      } else if (subscriptionData?.status === 'active') {
        // planType이 없으면 status로 판단 (하위 호환성)
        userInfo.subscriptionStatus = '플러스 플랜';
      } else {
        userInfo.subscriptionStatus = '무료 플랜';
      }

      // 필터링 적용
      let shouldInclude = true;

      // 구독 상태 필터
      if (statusFilter !== 'all') {
        const planType = subscriptionData?.planType || 'SubscriptionPlanType.free';
        if (statusFilter === 'plus' && planType !== 'SubscriptionPlanType.plus') {
          shouldInclude = false;
        } else if (statusFilter === 'free' && planType !== 'SubscriptionPlanType.free') {
          shouldInclude = false;
        }
      }

      // 검색 필터
      if (searchQuery && shouldInclude) {
        const searchLower = searchQuery.toLowerCase();
        const email = (userData.email || '').toLowerCase();
        const nickname = (userData.nickname || '').toLowerCase();
        const phone = (userData.phone || '').toLowerCase();

        if (!email.includes(searchLower) &&
            !nickname.includes(searchLower) &&
            !phone.includes(searchLower)) {
          shouldInclude = false;
        }
      }

      if (shouldInclude) {
        users.push(userInfo);
      }


    }

    // 전체 사용자 수 조회 (페이지네이션용)
    const totalUsersSnapshot = await db.collection('users').get();
    const totalUsers = totalUsersSnapshot.size;

    res.json({
      success: true,
      data: {
        users: users,
        pagination: {
          currentPage: pageNum,
          totalPages: Math.ceil(totalUsers / limitNum),
          totalUsers: totalUsers,
          hasNextPage: pageNum * limitNum < totalUsers,
          hasPrevPage: pageNum > 1
        }
      }
    });

  } catch (error) {
    console.error('사용자 목록 조회 오류:', error);
    res.status(500).json({
      success: false,
      error: '사용자 목록 조회 중 오류가 발생했습니다'
    });
  }
});



/**
 * 🔧 관리자 구독 상태 변경
 * 관리자가 사용자의 구독 상태를 변경할 수 있는 함수입니다.
 */
exports.adminSubscriptionManagement = onRequest({
  cors: true,
  invoker: 'public'
}, async (req, res) => {
  console.log('🔧 관리자 구독 관리 요청:', req.body);

  try {
    // 관리자 인증 확인
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: '인증이 필요합니다.'
      });
    }

    const { uid, action } = req.body;
    if (!uid || !action) {
      return res.status(400).json({
        success: false,
        error: 'uid와 action이 필요합니다.'
      });
    }

    const db = admin.firestore();
    const subscriptionRef = db.collection('users').doc(uid).collection('subscriptions').doc('current');
    const subscriptionDoc = await subscriptionRef.get();
    const currentSubscription = subscriptionDoc.exists ? subscriptionDoc.data() : null;

    let result = {};

    switch (action) {
      case 'add_days': {
        const { days } = req.body;
        if (!days || days <= 0) {
          return res.status(400).json({
            success: false,
            error: '올바른 일수를 입력하세요.'
          });
        }

        // 현재 구독이 활성화되어 있어야 함 (planType으로만 판단)
        const isActiveSubscription = currentSubscription && (
          currentSubscription.planType === 'SubscriptionPlanType.plus' ||
          currentSubscription.status === 'active'
        );

        if (!isActiveSubscription) {
          return res.status(400).json({
            success: false,
            error: '활성화된 구독이 없습니다.'
          });
        }

        // 현재 다음 결제일에 일수 추가
        const currentNextPayment = currentSubscription.nextPaymentDate?.toDate() || new Date();
        const newNextPayment = new Date(currentNextPayment.getTime() + (days * 24 * 60 * 60 * 1000));

        await subscriptionRef.update({
          // 기존 필드 업데이트
          nextPaymentDate: admin.firestore.Timestamp.fromDate(newNextPayment),
          subscriptionEndDate: newNextPayment.toISOString(),  // Flutter 앱 호환
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          adminExtended: true,
          extensionDays: (currentSubscription.extensionDays || 0) + days
        });

        result = {
          success: true,
          message: `구독 기간이 ${days}일 연장되었습니다.`,
          newEndDate: newNextPayment.toISOString()
        };
        break;
      }

      case 'cancel': {
        const isActiveSubscription = currentSubscription && (
          currentSubscription.planType === 'SubscriptionPlanType.plus' ||
          currentSubscription.status === 'active'
        );

        if (!isActiveSubscription) {
          return res.status(400).json({
            success: false,
            error: '활성화된 구독이 없습니다.'
          });
        }

        // 구독을 취소 예정 상태로 변경 (다음 결제일까지 유지)
        await subscriptionRef.update({
          status: 'cancel_scheduled',
          cancelledAt: admin.firestore.FieldValue.serverTimestamp(),
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          willCancelAt: currentSubscription.nextPaymentDate,
          adminCancelled: true
        });

        result = {
          success: true,
          message: '구독이 취소되었습니다. 다음 결제일까지 구독이 유지됩니다.'
        };
        break;
      }

      case 'force_free': {
        // 강제로 무료 플랜으로 전환
        await subscriptionRef.set({
          userId: uid,
          planType: 'SubscriptionPlanType.free',
          subscriptionStartDate: admin.firestore.FieldValue.serverTimestamp(),
          subscriptionEndDate: null,
          isActive: true,
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          status: 'free',
          plan: 'free',
          adminForced: true
        }, { merge: false }); // 기존 데이터 완전 덮어쓰기

        result = {
          success: true,
          message: '사용자가 강제로 무료 플랜으로 전환되었습니다.'
        };
        break;
      }

      case 'activate_until_date': {
        const { endDate } = req.body;
        if (!endDate) {
          return res.status(400).json({
            success: false,
            error: '종료 날짜가 필요합니다.'
          });
        }

        const endDateTime = new Date(endDate);
        if (endDateTime <= new Date()) {
          return res.status(400).json({
            success: false,
            error: '종료 날짜는 현재 시간보다 이후여야 합니다.'
          });
        }

        const now = new Date();

        // Flutter 앱 데이터 구조와 호환 (자동결제 없는 관리자 활성화)
        await subscriptionRef.set({
          // UserSubscription 모델 필드들
          userId: uid,
          planType: 'SubscriptionPlanType.plus',  // 핵심 필드!
          subscriptionStartDate: now.toISOString(),
          subscriptionEndDate: endDateTime.toISOString(),
          isActive: true,
          createdAt: currentSubscription?.createdAt || now.toISOString(),
          updatedAt: now.toISOString(),

          // 관리자 활성화 전용 상태 (자동결제 없음)
          status: 'admin_granted',  // 실제 구독과 구분!
          plan: 'plus',
          price: 0,  // 관리자 제공이므로 0원

          // 자동결제 관련 필드 제거 (중요!)
          // nextPaymentDate: 없음! (자동결제 방지)
          // subscriptionDay: 없음!
          // bid: 없음!

          // 관리자 액션 표시
          adminActivated: true,
          adminSetEndDate: endDateTime.toISOString(),
          grantedBy: 'admin'
        }, { merge: true });

        result = {
          success: true,
          message: `구독이 ${endDateTime.toLocaleDateString()}까지 활성화되었습니다.`
        };
        break;
      }

      case 'activate_for_days': {
        const { days } = req.body;
        if (!days || days <= 0) {
          return res.status(400).json({
            success: false,
            error: '올바른 일수를 입력하세요.'
          });
        }

        const now = new Date();
        const endDate = new Date(Date.now() + (days * 24 * 60 * 60 * 1000));

        // Flutter 앱 데이터 구조와 호환 (자동결제 없는 관리자 활성화)
        await subscriptionRef.set({
          // UserSubscription 모델 필드들
          userId: uid,
          planType: 'SubscriptionPlanType.plus',  // 핵심 필드!
          subscriptionStartDate: now.toISOString(),
          subscriptionEndDate: endDate.toISOString(),
          isActive: true,
          createdAt: currentSubscription?.createdAt || now.toISOString(),
          updatedAt: now.toISOString(),

          // 관리자 활성화 전용 상태 (자동결제 없음)
          status: 'admin_granted',  // 실제 구독과 구분!
          plan: 'plus',
          price: 0,  // 관리자 제공이므로 0원

          // 자동결제 관련 필드 제거 (중요!)
          // nextPaymentDate: 없음! (자동결제 방지)
          // subscriptionDay: 없음!
          // bid: 없음!

          // 관리자 액션 표시
          adminActivated: true,
          adminSetDays: days,
          grantedBy: 'admin'
        }, { merge: true });

        result = {
          success: true,
          message: `구독이 ${days}일간 활성화되었습니다.`
        };
        break;
      }

      case 'reactivate': {
        // 구독 취소 해제 (재구독)
        if (!currentSubscription || currentSubscription.status !== 'cancel_scheduled') {
          return res.status(400).json({
            success: false,
            error: '취소 예정인 구독이 없습니다.'
          });
        }

        await subscriptionRef.update({
          status: 'active',
          cancelledAt: admin.firestore.FieldValue.delete(),
          willCancelAt: admin.firestore.FieldValue.delete(),
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          adminReactivated: true
        });

        result = {
          success: true,
          message: '재구독이 완료되었습니다. 자동결제가 재개됩니다.'
        };
        break;
      }

      default:
        return res.status(400).json({
          success: false,
          error: '지원하지 않는 action입니다.'
        });
    }

    // 로그 저장
    const now = new Date();
    const kstTime = new Date(now.getTime() + (9 * 60 * 60 * 1000));
    const logTime = kstTime.toISOString().replace('T', ' ').substring(0, 19);
    const docName = `구독 관리 : ${logTime} KST`;

    await db.collection('logs').doc(docName).set({
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      type: 'subscription_management',
      totalProcessed: 1,
      successCount: 1,
      failureCount: 0,
      message: `관리자 구독 관리: ${action}`,
      level: 'SUCCESS',
      details: JSON.stringify({
        uid: uid,
        action: action,
        result: result,
        adminAction: true
      }),
      executedBy: 'Admin'
    });

    console.log(`✅ 사용자 ${uid} 구독 관리 완료: ${action}`);

    // result에 이미 success가 포함되어 있으므로 그대로 반환
    res.status(200).json(result);

  } catch (error) {
    console.error('❌ 관리자 구독 관리 오류:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});






/**
 * 🧭 관리자 시스템 정보 (실데이터)
 * - 최근 관리자 로그인 로그(admin_logs)
 * - 최근 자동 결제 로그(auto_payment_logs)
 * - 최근 접속 로그(access_logs)
 * - 최근 일일 사용량(usage_aggregated의 최신 문서)
 */
exports.getAdminSystemInfo = onRequest({ cors: true }, async (req, res) => {
  try {
    const db = admin.firestore();

    // 최근 관리자 로그 5건으로 축소 (읽기 최적화)
    const adminLogsSnap = await db.collection('admin_logs')
      .orderBy('timestamp', 'desc')
      .limit(5)
      .get();

    const adminLogs = adminLogsSnap.docs.map(d => ({ id: d.id, ...d.data() }));

    // 최근 통합 로그 5건으로 축소 (읽기 최적화)
    const logsSnap = await db.collection('logs')
      .orderBy('timestamp', 'desc')
      .limit(5)
      .get();

    const logs = logsSnap.docs.map(d => ({ id: d.id, ...d.data() }));

    // 최근 접속 로그 50건 (최신순)
    const accessLogsSnap = await db.collection('access_logs')
      .orderBy('timestamp', 'desc')
      .limit(50)
      .get();

    const accessLogs = accessLogsSnap.docs.map(d => ({ id: d.id, ...d.data() }));

    // usage_aggregated에서 가장 최근 날짜 문서 1건
    const aggSnap = await db.collection('usage_aggregated')
      .orderBy('date', 'desc')
      .limit(1)
      .get();

    let systemStats = {
      totalFunctionCalls: '-',
      successRate: '-',
      lastExecution: '-',
    };

    if (!aggSnap.empty) {
      const agg = aggSnap.docs[0].data();
      systemStats = {
        totalFunctionCalls: String(agg.totalActions ?? '-'),
        successRate: '-',
        lastExecution: agg.aggregatedAt ? agg.aggregatedAt.toDate().toLocaleString('ko-KR', { timeZone: 'Asia/Seoul' }) : '-',
      };
    }

    res.json({
      success: true,
      data: {
        adminLogs,
        logs,
        accessLogs,
        systemStats
      }
    });
  } catch (e) {
    console.error('getAdminSystemInfo error', e);
    res.status(500).json({ success: false, error: e.message });
  }
});

/**
 * 🗑️ 오래된 로그 데이터 정리 (HTTP 요청으로 실행)
 */
exports.cleanupOldLogsHttp = onRequest({ cors: true }, async (req, res) => {
  try {
    const db = admin.firestore();
    const now = new Date();

    // 30일 이전 데이터 삭제
    const thirtyDaysAgo = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));

    console.log('🗑️ 오래된 로그 정리 시작:', thirtyDaysAgo.toISOString());

    let totalDeleted = 0;

    // 1. 통합 로그 정리 (30일 이전)
    const logsQuery = db.collection('logs')
      .where('timestamp', '<', admin.firestore.Timestamp.fromDate(thirtyDaysAgo))
      .limit(500);

    const logsSnapshot = await logsQuery.get();
    if (!logsSnapshot.empty) {
      const batch1 = db.batch();
      logsSnapshot.docs.forEach(doc => batch1.delete(doc.ref));
      await batch1.commit();
      totalDeleted += logsSnapshot.size;
      console.log(`✅ 통합 로그 ${logsSnapshot.size}개 삭제`);
    }

    // 2. 접속 로그 정리 (3일 이전으로 단축)
    const threeDaysAgo = new Date(now.getTime() - (3 * 24 * 60 * 60 * 1000));
    const accessLogsQuery = db.collection('access_logs')
      .where('timestamp', '<', admin.firestore.Timestamp.fromDate(threeDaysAgo))
      .limit(1000); // 배치 크기 증가

    const accessSnapshot = await accessLogsQuery.get();
    if (!accessSnapshot.empty) {
      const batch2 = db.batch();
      accessSnapshot.docs.forEach(doc => batch2.delete(doc.ref));
      await batch2.commit();
      totalDeleted += accessSnapshot.size;
      console.log(`✅ 접속 로그 ${accessSnapshot.size}개 삭제`);
    }

    // 3. 관리자 로그 정리 (90일 이전)
    const ninetyDaysAgo = new Date(now.getTime() - (90 * 24 * 60 * 60 * 1000));
    const adminLogsQuery = db.collection('admin_logs')
      .where('timestamp', '<', admin.firestore.Timestamp.fromDate(ninetyDaysAgo))
      .limit(500);

    const adminSnapshot = await adminLogsQuery.get();
    if (!adminSnapshot.empty) {
      const batch3 = db.batch();
      adminSnapshot.docs.forEach(doc => batch3.delete(doc.ref));
      await batch3.commit();
      totalDeleted += adminSnapshot.size;
      console.log(`✅ 관리자 로그 ${adminSnapshot.size}개 삭제`);
    }

    // 정리 결과 로그 저장
    const currentTime = new Date();
    const kstTime = new Date(currentTime.getTime() + (9 * 60 * 60 * 1000));
    const logTime = kstTime.toISOString().replace('T', ' ').substring(0, 19);
    const docName = `로그 정리 : ${logTime} KST`;

    await db.collection('logs').doc(docName).set({
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      type: 'log_cleanup',
      totalProcessed: totalDeleted,
      successCount: totalDeleted,
      failureCount: 0,
      message: `오래된 로그 정리 완료: ${totalDeleted}개 삭제`,
      level: 'SUCCESS',
      details: JSON.stringify({
        logs: logsSnapshot?.size || 0,
        accessLogs: accessSnapshot?.size || 0,
        adminLogs: adminSnapshot?.size || 0,
        cutoffDates: {
          logs: thirtyDaysAgo.toISOString(),
          access: threeDaysAgo.toISOString(),
          admin: ninetyDaysAgo.toISOString()
        }
      }),
      executedBy: 'HTTP Request'
    });

    console.log(`🎉 로그 정리 완료: 총 ${totalDeleted}개 삭제`);

    res.status(200).json({
      success: true,
      message: `로그 정리 완료: 총 ${totalDeleted}개 삭제`,
      totalDeleted,
      details: {
        logs: logsSnapshot?.size || 0,
        accessLogs: accessSnapshot?.size || 0,
        adminLogs: adminSnapshot?.size || 0
      }
    });
  } catch (error) {
    console.error('❌ 로그 정리 오류:', error);

    // 오류 로그 저장
    try {
      await admin.firestore().collection('auto_payment_logs').add({
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        type: 'log_cleanup',
        totalProcessed: 0,
        successCount: 0,
        failureCount: 1,
        message: `로그 정리 오류: ${error.message}`,
        level: 'ERROR',
        details: JSON.stringify({
          error: error.message,
          stack: error.stack
        }),
        executedBy: 'HTTP Request'
      });
    } catch (logError) {
      console.error('❌ 로그 정리 오류 로그 저장 실패:', logError);
    }

    res.status(500).json({
      success: false,
      message: `로그 정리 오류: ${error.message}`,
      error: error.message
    });
  }
});









// ===== 📱 SMS 인증 시스템 (2025년 공식 문서 기반) =====

/**
 * 네이버 클라우드 플랫폼 SMS 설정
 */
const SMS_CONFIG = {
  SERVICE_ID: 'ncp:sms:kr:355114774538:parabara_sms',
  ACCESS_KEY: 'ncp_iam_BPAMKRiNhi7MeSHsgLac',
  SECRET_KEY: 'ncp_iam_BPKMKRfOWia3ZF1gV5NfKMH7kEZBgegpSD',
  FROM_NUMBER: '01023914308', // 승인된 발신번호
  BASE_URL: 'https://sens.apigw.ntruss.com'
};

/**
 * 6자리 인증번호 생성
 */
function generateSmsCode() {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

/**
 * 네이버 클라우드 API 서명 생성
 */
function createSignature(method, url, timestamp, accessKey, secretKey) {
  const crypto = require('crypto');
  const message = `${method} ${url}\n${timestamp}\n${accessKey}`;
  return crypto.createHmac('sha256', secretKey).update(message).digest('base64');
}

/**
 * 전화번호 중복 확인 함수 (SMS 발송 전 체크용)
 */
exports.checkPhoneDuplicate = onRequest({ cors: true }, async (req, res) => {
  try {
    const { phoneNumber, uid } = req.body;

    if (!phoneNumber) {
      return res.status(400).json({ success: false, message: '전화번호를 입력해주세요.' });
    }
    if (!uid) {
      return res.status(400).json({ success: false, message: '사용자 인증이 필요합니다.' });
    }

    console.log(`전화번호 중복 확인 요청: ${phoneNumber}, UID: ${uid}`);

    // 전화번호 중복 확인
    const existingPhoneQuery = await admin.firestore()
      .collection('users')
      .where('phone', '==', phoneNumber)
      .where('phoneVerified', '==', true)
      .get();

    // 현재 사용자가 아닌 다른 사용자가 이미 이 번호를 사용 중인지 확인
    const duplicateUser = existingPhoneQuery.docs.find(doc => doc.id !== uid);
    if (duplicateUser) {
      console.log(`전화번호 중복 발견: ${phoneNumber} (사용자: ${duplicateUser.id})`);
      return res.status(400).json({
        success: false,
        isDuplicate: true,
        message: '이미 다른 계정에서 사용 중인 전화번호입니다.'
      });
    }

    console.log(`전화번호 중복 확인 완료: ${phoneNumber} (사용 가능)`);
    res.json({
      success: true,
      isDuplicate: false,
      message: '사용 가능한 전화번호입니다.'
    });

  } catch (error) {
    console.error('전화번호 중복 확인 오류:', error);
    res.status(500).json({ success: false, message: error.message || '전화번호 중복 확인에 실패했습니다.' });
  }
});

/**
 * SMS 발송 함수 (공식 문서 기반)
 */
exports.sendSMSRequest = onRequest({ cors: true }, async (req, res) => {
  try {
    const { phoneNumber, recaptchaToken, uid } = req.body;

    // 기본 검증
    if (!phoneNumber || !/^01[0-9]{8,9}$/.test(phoneNumber.replace(/-/g, ''))) {
      return res.status(400).json({ success: false, message: '올바른 전화번호를 입력해주세요.' });
    }
    if (!recaptchaToken) {
      return res.status(400).json({ success: false, message: 'reCAPTCHA 인증을 완료해주세요.' });
    }
    if (!uid) {
      return res.status(400).json({ success: false, message: '사용자 인증이 필요합니다.' });
    }

    // 보안 검증 처리
    if (recaptchaToken === 'app_check_verified') {
      // Firebase App Check를 통한 검증 (네이티브 앱)
      console.log(`✅ Firebase App Check 검증 완료 (전화번호: ${phoneNumber})`);
      // App Check는 Firebase SDK에서 자동으로 처리되므로 추가 검증 불필요

      // 추가 보안 검증: IP 기반 제한 (선택사항)
      const clientIP = req.ip || req.connection.remoteAddress;
      console.log(`📍 클라이언트 IP: ${clientIP}`);


    } else {
      // 알 수 없는 토큰 형식
      console.log(`⚠️ 알 수 없는 보안 토큰 형식: ${recaptchaToken}`);
      return res.status(400).json({
        success: false,
        message: '지원되지 않는 보안 검증 방식입니다.'
      });
    }

    // 전화번호 중복 확인 (발송 전에 미리 체크)
    const existingPhoneQuery = await admin.firestore()
      .collection('users')
      .where('phone', '==', phoneNumber)
      .where('phoneVerified', '==', true)
      .get();

    // 현재 사용자가 아닌 다른 사용자가 이미 이 번호를 사용 중인지 확인
    const duplicateUser = existingPhoneQuery.docs.find(doc => doc.id !== uid);
    if (duplicateUser) {
      return res.status(400).json({
        success: false,
        message: '이미 다른 계정에서 사용 중인 전화번호입니다.'
      });
    }

    // 인증번호 생성
    const code = generateSmsCode();

    // SMS API 호출
    const timestamp = Date.now().toString();
    const url = `/sms/v2/services/${SMS_CONFIG.SERVICE_ID}/messages`;
    const signature = createSignature('POST', url, timestamp, SMS_CONFIG.ACCESS_KEY, SMS_CONFIG.SECRET_KEY);

    const smsData = {
      type: 'SMS',
      contentType: 'COMM',
      countryCode: '82',
      from: SMS_CONFIG.FROM_NUMBER,
      content: `[바라 부스 매니저] 인증번호: ${code}`,
      messages: [{ to: phoneNumber.replace(/-/g, '') }]
    };

    const response = await axios.post(`${SMS_CONFIG.BASE_URL}${url}`, smsData, {
      headers: {
        'Content-Type': 'application/json; charset=utf-8',
        'x-ncp-apigw-timestamp': timestamp,
        'x-ncp-iam-access-key': SMS_CONFIG.ACCESS_KEY,
        'x-ncp-apigw-signature-v2': signature
      }
    });

    if (response.status !== 202) {
      return res.status(500).json({ success: false, message: `SMS 발송 실패: HTTP ${response.status}` });
    }

    // Firestore에 인증번호 저장 (전화번호를 키로 사용, UID 포함)
    await admin.firestore().collection('sms_verifications').doc(phoneNumber).set({
      phoneNumber,
      uid,
      code,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      expiresAt: new Date(Date.now() + 5 * 60 * 1000),
      verified: false
    });

    console.log(`SMS 발송 성공: ${phoneNumber}`);
    res.json({ success: true, message: '인증번호가 발송되었습니다.' });

  } catch (error) {
    console.error('SMS 발송 오류:', error);
    res.status(500).json({ success: false, message: error.message || 'SMS 발송에 실패했습니다.' });
  }
});

/**
 * SMS 인증 확인 함수
 */
exports.verifySMSRequest = onRequest({ cors: true }, async (req, res) => {
  try {
    const { phoneNumber, code, uid } = req.body;

    if (!phoneNumber || !code) {
      return res.status(400).json({ success: false, message: '전화번호와 인증번호를 입력해주세요.' });
    }
    if (!uid) {
      return res.status(400).json({ success: false, message: '사용자 인증이 필요합니다.' });
    }

    // 인증번호 확인 (전화번호를 키로 사용)
    const verificationRef = admin.firestore().collection('sms_verifications').doc(phoneNumber);
    const doc = await verificationRef.get();

    if (!doc.exists) {
      return res.status(400).json({ success: false, message: '인증번호 발송 기록이 없습니다.' });
    }

    const data = doc.data();
    if (data.expiresAt.toDate() < new Date()) {
      return res.status(400).json({ success: false, message: '인증번호가 만료되었습니다.' });
    }
    if (data.code !== code) {
      return res.status(400).json({ success: false, message: '인증번호가 일치하지 않습니다.' });
    }
    if (data.verified) {
      return res.status(400).json({ success: false, message: '이미 인증된 전화번호입니다.' });
    }
    if (data.uid !== uid) {
      return res.status(400).json({ success: false, message: '사용자 정보가 일치하지 않습니다.' });
    }

    // 인증 완료 처리
    await verificationRef.update({
      verified: true,
      verifiedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    // 핸드폰 번호 중복 확인 (다른 사용자가 이미 사용 중인지 체크)
    const existingPhoneQuery = await admin.firestore()
      .collection('users')
      .where('phone', '==', phoneNumber)
      .where('phoneVerified', '==', true)
      .get();

    // 현재 사용자가 아닌 다른 사용자가 이미 이 번호를 사용 중인지 확인
    const duplicateUser = existingPhoneQuery.docs.find(doc => doc.id !== uid);
    if (duplicateUser) {
      return res.status(400).json({
        success: false,
        message: '이미 다른 계정에서 사용 중인 전화번호입니다.'
      });
    }

    // 기존 핸드폰 번호 문서 삭제 (사용자가 번호를 변경하는 경우)
    const currentUserDoc = await admin.firestore().collection('users').doc(uid).get();
    if (currentUserDoc.exists && currentUserDoc.data().phone &&
        currentUserDoc.data().phone !== phoneNumber) {
      try {
        await admin.firestore()
          .collection('phone_numbers')
          .doc(currentUserDoc.data().phone)
          .delete();
      } catch (e) {
        console.log('기존 핸드폰 번호 문서 삭제 실패 (무시):', e.message);
      }
    }

    // 트랜잭션으로 사용자 문서와 핸드폰 번호 문서 동시 업데이트
    await admin.firestore().runTransaction(async (transaction) => {
      const userRef = admin.firestore().collection('users').doc(uid);
      const phoneRef = admin.firestore().collection('phone_numbers').doc(phoneNumber);

      // 사용자 문서 업데이트
      transaction.set(userRef, {
        phone: phoneNumber,
        phoneVerified: true,
        phoneVerifiedAt: admin.firestore.FieldValue.serverTimestamp()
      }, { merge: true });

      // 핸드폰 번호 소유권 문서 생성/업데이트
      transaction.set(phoneRef, {
        userId: uid,
        phoneNumber: phoneNumber,
        verifiedAt: admin.firestore.FieldValue.serverTimestamp(),
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      });
    });

    console.log(`SMS 인증 완료: ${phoneNumber}, UID: ${uid}`);
    res.json({ success: true, message: '전화번호 인증이 완료되었습니다.' });

  } catch (error) {
    console.error('SMS 인증 확인 오류:', error);
    res.status(500).json({ success: false, message: error.message || '인증번호 확인에 실패했습니다.' });
  }
});
