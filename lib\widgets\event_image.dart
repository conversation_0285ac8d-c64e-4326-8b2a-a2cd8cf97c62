import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'dart:io';

import '../utils/image_sync_utils.dart';
import '../utils/logger_utils.dart';

import '../widgets/skeleton_loading.dart';
import '../utils/app_colors.dart';
import 'package:lucide_icons/lucide_icons.dart';

/// 행사 이미지를 최적화하여 표시하는 공통 위젯입니다.
/// - 로컬 파일 우선, 없으면 네트워크 이미지 사용
/// - 캐싱 최적화로 성능 향상 및 깜빡임 방지
/// - 스켈레톤 로딩으로 더 나은 UX 제공
/// - ProductImage와 동일한 스마트 로딩 로직 적용
class EventImage extends StatefulWidget {
  final String? imagePath;
  final BoxFit? fit;
  final double? width;
  final double? height;
  final BorderRadius? borderRadius;

  const EventImage({
    super.key,
    required this.imagePath,
    this.fit,
    this.width,
    this.height,
    this.borderRadius,
  });

  @override
  State<EventImage> createState() => _EventImageState();
}

class _EventImageState extends State<EventImage> {
  Widget? _imageWidget;
  bool _isLoading = true;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  @override
  void didUpdateWidget(EventImage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.imagePath != widget.imagePath) {
      _loadImage();
    }
  }

  /// 이미지 로딩 처리
  Future<void> _loadImage() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      final imageWidget = await _determineImageWidget();
      if (mounted) {
        setState(() {
          _imageWidget = imageWidget;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _hasError = true;
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // 성능 최적화를 위한 RepaintBoundary 적용
    return RepaintBoundary(
      child: ClipRRect(
        borderRadius: widget.borderRadius ?? BorderRadius.zero,
        child: _isLoading
            ? _buildSkeletonLoader()
            : _hasError
                ? _buildErrorWidget()
                : _imageWidget ?? _buildErrorWidget(),
      ),
    );
  }

  /// 로컬 파일 우선, 없으면 네트워크 이미지 사용하는 위젯 결정 (안정적인 로딩)
  Future<Widget> _determineImageWidget() async {
    // imagePath가 null이거나 비어있으면 기본 이미지
    if (widget.imagePath == null || widget.imagePath!.isEmpty) {
      return _buildDefaultEventImage();
    }

    // 1. 로컬 파일인지 확인
    if (ImageSyncUtils.isLocalImagePath(widget.imagePath!)) {
      final file = File(widget.imagePath!);
      try {
        final exists = await file.exists();
        if (exists) {
          // 파일 크기도 확인하여 유효한 이미지인지 검증
          final fileSize = await file.length();
          if (fileSize > 0) {
            LoggerUtils.logDebug('행사 이미지 파일 확인 성공: ${widget.imagePath} (${fileSize}bytes)', tag: 'EventImage');
            return _buildLocalImage();
          } else {
            LoggerUtils.logWarning('행사 이미지 파일이 비어있음: ${widget.imagePath}', tag: 'EventImage');
          }
        } else {
          LoggerUtils.logWarning('행사 이미지 파일이 존재하지 않음: ${widget.imagePath}', tag: 'EventImage');
        }
      } catch (e) {
        LoggerUtils.logError('행사 이미지 파일 확인 실패: ${widget.imagePath}', tag: 'EventImage', error: e);
      }
    }

    // 2. 네트워크 URL인지 확인
    if (ImageSyncUtils.isNetworkImagePath(widget.imagePath!)) {
      LoggerUtils.logDebug('네트워크 행사 이미지 로딩: ${widget.imagePath}', tag: 'EventImage');
      return _buildNetworkImage();
    }

    // 3. 둘 다 아니면 기본 이미지
    LoggerUtils.logWarning('유효하지 않은 행사 이미지 경로: ${widget.imagePath}', tag: 'EventImage');
    return _buildDefaultEventImage();
  }

  /// 로컬 이미지 위젯 생성
  Widget _buildLocalImage() {
    return _buildLocalImageWithPath(widget.imagePath!);
  }

  /// 지정된 경로로 로컬 이미지 위젯 생성 (ProductImage와 동일한 안정적인 로직)
  Widget _buildLocalImageWithPath(String imagePath) {
    // 파일 존재 여부와 수정 시간을 안전하게 확인
    String cacheKey = imagePath;
    try {
      final file = File(imagePath);
      if (file.existsSync()) {
        final lastModified = file.lastModifiedSync().millisecondsSinceEpoch;
        cacheKey = '${imagePath}_$lastModified';
      } else {
        LoggerUtils.logWarning('행사 이미지 파일이 존재하지 않음: $imagePath', tag: 'EventImage');
        return _buildDefaultEventImage();
      }
    } catch (e) {
      LoggerUtils.logError('행사 이미지 파일 정보 확인 실패: $imagePath', tag: 'EventImage', error: e);
      return _buildDefaultEventImage();
    }

    return Image.file(
      File(imagePath),
      fit: widget.fit ?? BoxFit.cover,
      width: widget.width,
      height: widget.height,
      filterQuality: FilterQuality.medium,
      cacheWidth: (widget.width?.toInt() ?? 400).clamp(200, 600),
      cacheHeight: (widget.height?.toInt() ?? 400).clamp(200, 600),
      // 안전하게 생성된 캐시 키 사용
      key: ValueKey(cacheKey),
      errorBuilder: (context, error, stackTrace) {
        LoggerUtils.logError('행사 이미지 위젯 로딩 실패: $imagePath', tag: 'EventImage', error: error);
        return _buildDefaultEventImage();
      },
    );
  }



  /// 네트워크 이미지 위젯 생성
  Widget _buildNetworkImage() {
    return CachedNetworkImage(
      imageUrl: widget.imagePath!,
      fit: widget.fit ?? BoxFit.cover,
      width: widget.width,
      height: widget.height,
      placeholder: (context, url) => _buildSkeletonLoader(),
      errorWidget: (context, url, error) => _buildDefaultEventImage(),
      memCacheWidth: (widget.width?.toInt() ?? 400).clamp(200, 600),
      memCacheHeight: (widget.height?.toInt() ?? 400).clamp(200, 600),
      maxWidthDiskCache: 400,
      maxHeightDiskCache: 400,
    );
  }

  /// 스켈레톤 로더 위젯
  Widget _buildSkeletonLoader() {
    return SkeletonLoading.image(
      width: widget.width,
      height: widget.height,
      borderRadius: widget.borderRadius,
    );
  }

  /// 기본 행사 이미지 (플레이스홀더)
  Widget _buildDefaultEventImage() {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        gradient: AppColors.primaryGradient,
        borderRadius: widget.borderRadius,
      ),
      child: Icon(
        LucideIcons.calendar,
        color: AppColors.onPrimary,
        size: (widget.width != null && widget.height != null) 
            ? (widget.width! * 0.4).clamp(16, 48)
            : 32,
      ),
    );
  }

  /// 에러 위젯 (기본 이미지와 동일)
  Widget _buildErrorWidget() {
    return _buildDefaultEventImage();
  }
}
